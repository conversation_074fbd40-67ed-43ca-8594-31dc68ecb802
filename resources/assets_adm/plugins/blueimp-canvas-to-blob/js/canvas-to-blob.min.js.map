{"version": 3, "sources": ["canvas-to-blob.js"], "names": ["window", "CanvasPrototype", "HTMLCanvasElement", "prototype", "hasBlobConstructor", "Blob", "Boolean", "e", "hasArrayBufferViewSupport", "Uint8Array", "size", "BlobBuilder", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "dataURIPattern", "dataURLtoBlob", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataURI", "mediaType", "byteString", "arrayBuffer", "intArray", "i", "matches", "match", "Error", "isBase64", "dataString", "slice", "length", "decodeURIComponent", "charCodeAt", "type", "bb", "append", "getBlob", "toBlob", "mozGetAsFile", "callback", "quality", "self", "this", "setTimeout", "toDataURL", "msToBlob", "define", "amd", "module", "exports"], "mappings": "CAgBC,SAAWA,gBAGV,IAAIC,EACFD,EAAOE,mBAAqBF,EAAOE,kBAAkBC,UACnDC,EACFJ,EAAOK,MACP,WACE,IACE,OAAOC,QAAQ,IAAID,MACnB,MAAOE,GACP,OAAO,GAJX,GAOEC,EACFJ,GACAJ,EAAOS,YACP,WACE,IACE,OAAgD,MAAzC,IAAIJ,KAAK,CAAC,IAAII,WAAW,OAAOC,KACvC,MAAOH,GACP,OAAO,GAJX,GAOEI,EACFX,EAAOW,aACPX,EAAOY,mBACPZ,EAAOa,gBACPb,EAAOc,cACLC,EAAiB,0CACjBC,GACDZ,GAAsBO,IACvBX,EAAOiB,MACPjB,EAAOkB,aACPlB,EAAOS,YACP,SAAUU,GACR,IACEC,EAGAC,EACAC,EACAC,EACAC,EAGFC,EAAUN,EAAQO,MAAMX,GACxB,IAAKU,EACH,MAAM,IAAIE,MAAM,oBAkBlB,IAfAP,EAAYK,EAAQ,GAChBA,EAAQ,GACR,cAAgBA,EAAQ,IAAM,qBAClCG,IAAaH,EAAQ,GACrBI,EAAaV,EAAQW,MAAML,EAAQ,GAAGM,QAGpCV,GAFEO,EAEWX,KAGAe,oBAHKH,GAMpBP,EAAc,IAAIJ,YAAYG,EAAWU,QACzCR,EAAW,IAAId,WAAWa,GACrBE,EAAI,EAAGA,EAAIH,EAAWU,OAAQP,GAAK,EACtCD,EAASC,GAAKH,EAAWY,WAAWT,GAGtC,OAAIpB,EACK,IAAIC,KAAK,CAACG,EAA4Be,EAAWD,GAAc,CACpEY,KAAMd,MAGVe,EAAK,IAAIxB,GACNyB,OAAOd,GACHa,EAAGE,QAAQjB,KAElBpB,EAAOE,oBAAsBD,EAAgBqC,SAC3CrC,EAAgBsC,aAClBtC,EAAgBqC,OAAS,SAAUE,EAAUN,EAAMO,GACjD,IAAIC,EAAOC,KACXC,WAAW,WACLH,GAAWxC,EAAgB4C,WAAa7B,EAC1CwB,EAASxB,EAAc0B,EAAKG,UAAUX,EAAMO,KAE5CD,EAASE,EAAKH,aAAa,OAAQL,OAIhCjC,EAAgB4C,WAAa7B,IAClCf,EAAgB6C,SAClB7C,EAAgBqC,OAAS,SAAUE,EAAUN,EAAMO,GACjD,IAAIC,EAAOC,KACXC,WAAW,YAELV,GAAiB,cAATA,GAAyBO,IACnCxC,EAAgB4C,WAChB7B,EAEAwB,EAASxB,EAAc0B,EAAKG,UAAUX,EAAMO,KAE5CD,EAASE,EAAKI,SAASZ,OAK7BjC,EAAgBqC,OAAS,SAAUE,EAAUN,EAAMO,GACjD,IAAIC,EAAOC,KACXC,WAAW,WACTJ,EAASxB,EAAc0B,EAAKG,UAAUX,EAAMO,UAMhC,mBAAXM,QAAyBA,OAAOC,IACzCD,OAAO,WACL,OAAO/B,IAEkB,iBAAXiC,QAAuBA,OAAOC,QAC9CD,OAAOC,QAAUlC,EAEjBhB,EAAOgB,cAAgBA,EA5H1B,CA8HEhB"}