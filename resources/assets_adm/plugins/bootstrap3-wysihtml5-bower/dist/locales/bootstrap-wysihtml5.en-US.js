/**
 * English translation for bootstrap-wysihtml5
 */
(function (factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define('bootstrap.wysihtml5.en-US', ['jquery', 'bootstrap.wysihtml5'], factory);
    } else {
        // Browser globals
        factory(jQuery);
    }
}(function ($) {
  $.fn.wysihtml5.locale.en = $.fn.wysihtml5.locale['en-US'] = {
    font_styles: {
      normal: 'Normal text',
      h1: 'Heading 1',
      h2: 'Heading 2',
      h3: 'Heading 3',
      h4: 'Heading 4',
      h5: 'Heading 5',
      h6: 'Heading 6'
    },
    emphasis: {
      bold: 'Bold',
      italic: 'Italic',
      underline: 'Underline',
      small: 'Small'
    },
    lists: {
      unordered: 'Unordered list',
      ordered: 'Ordered list',
      outdent: 'Outdent',
      indent: 'Indent'
    },
    link: {
      insert: 'Insert link',
      cancel: 'Cancel',
      target: 'Open link in new window'
    },
    image: {
      insert: 'Insert image',
      cancel: 'Cancel'
    },
    html: {
      edit: 'Edit HTML'
    },
    colours: {
      black: 'Black',
      silver: 'Silver',
      gray: 'Grey',
      maroon: 'Maroon',
      red: 'Red',
      purple: 'Purple',
      green: 'Green',
      olive: 'Olive',
      navy: 'Navy',
      blue: 'Blue',
      orange: 'Orange'
    }
  };
}));
