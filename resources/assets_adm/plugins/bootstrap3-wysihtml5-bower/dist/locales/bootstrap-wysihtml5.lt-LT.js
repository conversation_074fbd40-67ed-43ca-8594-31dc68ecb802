/**
 * Lithuanian translation for bootstrap-wysihtml5
 */
(function (factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define('bootstrap.wysihtml5.lt-LT', ['jquery', 'bootstrap.wysihtml5'], factory);
    } else {
        // Browser globals
        factory(jQuery);
    }
}(function($){
    $.fn.wysihtml5.locale["lt-LT"] = {
        font_styles: {
              normal: "Normalus",
              h1: "Antraštė 1",
              h2: "Antraštė 2",
              h3: "Antraštė 3"
        },
        emphasis: {
              bold: "Pastorintas",
              italic: "Kurs<PERSON><PERSON>",
              underline: "<PERSON><PERSON><PERSON><PERSON>"
        },
        lists: {
              unordered: "Suženklintas sąrašas",
              ordered: "Numeruotas sąrašas",
              outdent: "Padidinti įtrauką",
              indent: "Sumažinti įtrauką"
        },
        link: {
              insert: "Įterpti nuorodą",
              cancel: "<PERSON><PERSON><PERSON><PERSON>"
        },
        image: {
              insert: "Įterpti atvaizdą",
              cancel: "At<PERSON><PERSON>ti"
        },
        html: {
            edit: "Redaguoti HTML"
        },
        colours: {
            black: "<PERSON>oda",
            silver: "Sidabrinė",
            gray: "<PERSON>lka",
            maroon: "Ka<PERSON>toninė",
            red: "Raudona",
            purple: "Violetinė",
            green: "Žalia",
            olive: "Gelsvai žalia",
            navy: "Tamsiai mėlyna",
            blue: "Mėlyna",
            orange: "Oranžinė"
        }
    };
}));
