{"version": 3, "file": "ui-router-angularjs.min.js", "sources": ["angular-ui-router/src/angular.ts", "angular-ui-router/src/statebuilders/views.ts", "angular-ui-router/src/templateFactory.ts", "angular-ui-router/src/stateProvider.ts", "angular-ui-router/src/statebuilders/onEnterExitRetain.ts", "angular-ui-router/src/locationServices.ts", "angular-ui-router/src/urlRouterProvider.ts", "angular-ui-router/src/services.ts", "angular-ui-router/src/directives/stateDirectives.ts", "angular-ui-router/src/directives/viewDirective.ts", "angular-ui-router/src/stateFilters.ts", "angular-ui-router/src/viewScroll.ts", "angular-ui-router/src/index.ts"], "sourcesContent": ["/** @publicapi @module ng1 */ /** */\nimport * as ng_from_import from 'angular';\n/** @hidden */ declare let angular;\n/** @hidden */ const ng_from_global = angular;\n/** @hidden */ export const ng = ng_from_import && ng_from_import.module ? ng_from_import : ng_from_global;\n", "/** @publicapi @module ng1 */ /** */\nimport {\n  StateObject,\n  pick,\n  forEach,\n  tail,\n  extend,\n  isArray,\n  isInjectable,\n  isDefined,\n  isString,\n  services,\n  trace,\n  ViewConfig,\n  ViewService,\n  ViewConfigFactory,\n  PathNode,\n  ResolveContext,\n  Resolvable,\n  IInjectable,\n} from '@uirouter/core';\nimport { Ng1ViewDeclaration } from '../interface';\nimport { TemplateFactory } from '../templateFactory';\n\n/** @internalapi */\nexport function getNg1ViewConfigFactory(): ViewConfigFactory {\n  let templateFactory: TemplateFactory = null;\n  return (path, view) => {\n    templateFactory = templateFactory || services.$injector.get('$templateFactory');\n    return [new Ng1ViewConfig(path, view, templateFactory)];\n  };\n}\n\n/** @internalapi */\nconst hasAnyKey = (keys, obj) => keys.reduce((acc, key) => acc || isDefined(obj[key]), false);\n\n/**\n * This is a [[StateBuilder.builder]] function for angular1 `views`.\n *\n * When the [[StateBuilder]] builds a [[StateObject]] object from a raw [[StateDeclaration]], this builder\n * handles the `views` property with logic specific to @uirouter/angularjs (ng1).\n *\n * If no `views: {}` property exists on the [[StateDeclaration]], then it creates the `views` object\n * and applies the state-level configuration to a view named `$default`.\n *\n * @internalapi\n */\nexport function ng1ViewsBuilder(state: StateObject) {\n  // Do not process root state\n  if (!state.parent) return {};\n\n  const tplKeys = ['templateProvider', 'templateUrl', 'template', 'notify', 'async'],\n    ctrlKeys = ['controller', 'controllerProvider', 'controllerAs', 'resolveAs'],\n    compKeys = ['component', 'bindings', 'componentProvider'],\n    nonCompKeys = tplKeys.concat(ctrlKeys),\n    allViewKeys = compKeys.concat(nonCompKeys);\n\n  // Do not allow a state to have both state-level props and also a `views: {}` property.\n  // A state without a `views: {}` property can declare properties for the `$default` view as properties of the state.\n  // However, the `$default` approach should not be mixed with a separate `views: ` block.\n  if (isDefined(state.views) && hasAnyKey(allViewKeys, state)) {\n    throw new Error(\n      `State '${state.name}' has a 'views' object. ` +\n        `It cannot also have \"view properties\" at the state level.  ` +\n        `Move the following properties into a view (in the 'views' object): ` +\n        ` ${allViewKeys.filter((key) => isDefined(state[key])).join(', ')}`\n    );\n  }\n\n  const views: { [key: string]: Ng1ViewDeclaration } = {},\n    viewsObject = state.views || { $default: pick(state, allViewKeys) };\n\n  forEach(viewsObject, function (config: Ng1ViewDeclaration, name: string) {\n    // Account for views: { \"\": { template... } }\n    name = name || '$default';\n    // Account for views: { header: \"headerComponent\" }\n    if (isString(config)) config = { component: <string>config };\n\n    // Make a shallow copy of the config object\n    config = extend({}, config);\n\n    // Do not allow a view to mix props for component-style view with props for template/controller-style view\n    if (hasAnyKey(compKeys, config) && hasAnyKey(nonCompKeys, config)) {\n      throw new Error(\n        `Cannot combine: ${compKeys.join('|')} with: ${nonCompKeys.join('|')} in stateview: '${name}@${state.name}'`\n      );\n    }\n\n    config.resolveAs = config.resolveAs || '$resolve';\n    config.$type = 'ng1';\n    config.$context = state;\n    config.$name = name;\n\n    const normalized = ViewService.normalizeUIViewTarget(config.$context, config.$name);\n    config.$uiViewName = normalized.uiViewName;\n    config.$uiViewContextAnchor = normalized.uiViewContextAnchor;\n\n    views[name] = config;\n  });\n  return views;\n}\n\n/** @hidden */\nlet id = 0;\n\n/** @internalapi */\nexport class Ng1ViewConfig implements ViewConfig {\n  $id = id++;\n  loaded = false;\n  controller: Function; // actually IInjectable|string\n  template: string;\n  component: string;\n  locals: any; // TODO: delete me\n\n  constructor(public path: PathNode[], public viewDecl: Ng1ViewDeclaration, public factory: TemplateFactory) {}\n\n  load() {\n    const $q = services.$q;\n    const context = new ResolveContext(this.path);\n    const params = this.path.reduce((acc, node) => extend(acc, node.paramValues), {});\n\n    const promises: any = {\n      template: $q.when(this.factory.fromConfig(this.viewDecl, params, context)),\n      controller: $q.when(this.getController(context)),\n    };\n\n    return $q.all(promises).then((results) => {\n      trace.traceViewServiceEvent('Loaded', this);\n      this.controller = results.controller;\n      extend(this, results.template); // Either { template: \"tpl\" } or { component: \"cmpName\" }\n      return this;\n    });\n  }\n\n  getTemplate = (uiView, context: ResolveContext) =>\n    this.component\n      ? this.factory.makeComponentTemplate(uiView, context, this.component, this.viewDecl.bindings)\n      : this.template;\n\n  /**\n   * Gets the controller for a view configuration.\n   *\n   * @returns {Function|Promise.<Function>} Returns a controller, or a promise that resolves to a controller.\n   */\n  getController(context: ResolveContext): IInjectable | string | Promise<IInjectable | string> {\n    const provider = this.viewDecl.controllerProvider;\n    if (!isInjectable(provider)) return this.viewDecl.controller;\n    const deps = services.$injector.annotate(provider);\n    const providerFn = isArray(provider) ? tail(<any>provider) : provider;\n    const resolvable = new Resolvable('', <any>providerFn, deps);\n    return resolvable.get(context);\n  }\n}\n", "/** @publicapi @module view */ /** */\nimport { ng as angular } from './angular';\nimport { IAugmentedJQuery } from 'angular';\nimport {\n  isArray,\n  isDefined,\n  isFunction,\n  isObject,\n  services,\n  Obj,\n  IInjectable,\n  tail,\n  kebobString,\n  unnestR,\n  ResolveContext,\n  Resolvable,\n  RawParams,\n} from '@uirouter/core';\nimport { Ng1ViewDeclaration, TemplateFactoryProvider } from './interface';\n\n/**\n * Service which manages loading of templates from a ViewConfig.\n */\nexport class TemplateFactory implements TemplateFactoryProvider {\n  /** @hidden */ private _useHttp = angular.version.minor < 3;\n  /** @hidden */ private $templateRequest;\n  /** @hidden */ private $templateCache;\n  /** @hidden */ private $http;\n\n  /** @hidden */ $get = [\n    '$http',\n    '$templateCache',\n    '$injector',\n    ($http, $templateCache, $injector) => {\n      this.$templateRequest = $injector.has && $injector.has('$templateRequest') && $injector.get('$templateRequest');\n      this.$http = $http;\n      this.$templateCache = $templateCache;\n      return this;\n    },\n  ];\n\n  /** @hidden */\n  useHttpService(value: boolean) {\n    this._useHttp = value;\n  }\n\n  /**\n   * Creates a template from a configuration object.\n   *\n   * @param config Configuration object for which to load a template.\n   * The following properties are search in the specified order, and the first one\n   * that is defined is used to create the template:\n   *\n   * @param params  Parameters to pass to the template function.\n   * @param context The resolve context associated with the template's view\n   *\n   * @return {string|object}  The template html as a string, or a promise for\n   * that string,or `null` if no template is configured.\n   */\n  fromConfig(\n    config: Ng1ViewDeclaration,\n    params: any,\n    context: ResolveContext\n  ): Promise<{ template?: string; component?: string }> {\n    const defaultTemplate = '<ui-view></ui-view>';\n\n    const asTemplate = (result) => services.$q.when(result).then((str) => ({ template: str }));\n    const asComponent = (result) => services.$q.when(result).then((str) => ({ component: str }));\n\n    return isDefined(config.template)\n      ? asTemplate(this.fromString(config.template, params))\n      : isDefined(config.templateUrl)\n      ? asTemplate(this.fromUrl(config.templateUrl, params))\n      : isDefined(config.templateProvider)\n      ? asTemplate(this.fromProvider(config.templateProvider, params, context))\n      : isDefined(config.component)\n      ? asComponent(config.component)\n      : isDefined(config.componentProvider)\n      ? asComponent(this.fromComponentProvider(config.componentProvider, params, context))\n      : asTemplate(defaultTemplate);\n  }\n\n  /**\n   * Creates a template from a string or a function returning a string.\n   *\n   * @param template html template as a string or function that returns an html template as a string.\n   * @param params Parameters to pass to the template function.\n   *\n   * @return {string|object} The template html as a string, or a promise for that\n   * string.\n   */\n  fromString(template: string | Function, params?: RawParams) {\n    return isFunction(template) ? (<any>template)(params) : template;\n  }\n\n  /**\n   * Loads a template from the a URL via `$http` and `$templateCache`.\n   *\n   * @param {string|Function} url url of the template to load, or a function\n   * that returns a url.\n   * @param {Object} params Parameters to pass to the url function.\n   * @return {string|Promise.<string>} The template html as a string, or a promise\n   * for that string.\n   */\n  fromUrl(url: string | Function, params: any) {\n    if (isFunction(url)) url = (<any>url)(params);\n    if (url == null) return null;\n\n    if (this._useHttp) {\n      return this.$http\n        .get(url, { cache: this.$templateCache, headers: { Accept: 'text/html' } })\n        .then(function (response) {\n          return response.data;\n        });\n    }\n\n    return this.$templateRequest(url);\n  }\n\n  /**\n   * Creates a template by invoking an injectable provider function.\n   *\n   * @param provider Function to invoke via `locals`\n   * @param {Function} injectFn a function used to invoke the template provider\n   * @return {string|Promise.<string>} The template html as a string, or a promise\n   * for that string.\n   */\n  fromProvider(provider: IInjectable, params: any, context: ResolveContext) {\n    const deps = services.$injector.annotate(provider);\n    const providerFn = isArray(provider) ? tail(<any[]>provider) : provider;\n    const resolvable = new Resolvable('', <Function>providerFn, deps);\n    return resolvable.get(context);\n  }\n\n  /**\n   * Creates a component's template by invoking an injectable provider function.\n   *\n   * @param provider Function to invoke via `locals`\n   * @param {Function} injectFn a function used to invoke the template provider\n   * @return {string} The template html as a string: \"<component-name input1='::$resolve.foo'></component-name>\".\n   */\n  fromComponentProvider(provider: IInjectable, params: any, context: ResolveContext) {\n    const deps = services.$injector.annotate(provider);\n    const providerFn = isArray(provider) ? tail(<any[]>provider) : provider;\n    const resolvable = new Resolvable('', <Function>providerFn, deps);\n    return resolvable.get(context);\n  }\n\n  /**\n   * Creates a template from a component's name\n   *\n   * This implements route-to-component.\n   * It works by retrieving the component (directive) metadata from the injector.\n   * It analyses the component's bindings, then constructs a template that instantiates the component.\n   * The template wires input and output bindings to resolves or from the parent component.\n   *\n   * @param uiView {object} The parent ui-view (for binding outputs to callbacks)\n   * @param context The ResolveContext (for binding outputs to callbacks returned from resolves)\n   * @param component {string} Component's name in camel case.\n   * @param bindings An object defining the component's bindings: {foo: '<'}\n   * @return {string} The template as a string: \"<component-name input1='::$resolve.foo'></component-name>\".\n   */\n  makeComponentTemplate(uiView: IAugmentedJQuery, context: ResolveContext, component: string, bindings?: any) {\n    bindings = bindings || {};\n\n    // Bind once prefix\n    const prefix = angular.version.minor >= 3 ? '::' : '';\n    // Convert to kebob name. Add x- prefix if the string starts with `x-` or `data-`\n    const kebob = (camelCase: string) => {\n      const kebobed = kebobString(camelCase);\n      return /^(x|data)-/.exec(kebobed) ? `x-${kebobed}` : kebobed;\n    };\n\n    const attributeTpl = (input: BindingTuple) => {\n      const { name, type } = input;\n      const attrName = kebob(name);\n      // If the ui-view has an attribute which matches a binding on the routed component\n      // then pass that attribute through to the routed component template.\n      // Prefer ui-view wired mappings to resolve data, unless the resolve was explicitly bound using `bindings:`\n      if (uiView.attr(attrName) && !bindings[name]) return `${attrName}='${uiView.attr(attrName)}'`;\n\n      const resolveName = bindings[name] || name;\n      // Pre-evaluate the expression for \"@\" bindings by enclosing in {{ }}\n      // some-attr=\"{{ ::$resolve.someResolveName }}\"\n      if (type === '@') return `${attrName}='{{${prefix}$resolve.${resolveName}}}'`;\n\n      // Wire \"&\" callbacks to resolves that return a callback function\n      // Get the result of the resolve (should be a function) and annotate it to get its arguments.\n      // some-attr=\"$resolve.someResolveResultName(foo, bar)\"\n      if (type === '&') {\n        const res = context.getResolvable(resolveName);\n        const fn = res && res.data;\n        const args = (fn && services.$injector.annotate(fn)) || [];\n        // account for array style injection, i.e., ['foo', function(foo) {}]\n        const arrayIdxStr = isArray(fn) ? `[${fn.length - 1}]` : '';\n        return `${attrName}='$resolve.${resolveName}${arrayIdxStr}(${args.join(',')})'`;\n      }\n\n      // some-attr=\"::$resolve.someResolveName\"\n      return `${attrName}='${prefix}$resolve.${resolveName}'`;\n    };\n\n    const attrs = getComponentBindings(component).map(attributeTpl).join(' ');\n    const kebobName = kebob(component);\n    return `<${kebobName} ${attrs}></${kebobName}>`;\n  }\n}\n\n// Gets all the directive(s)' inputs ('@', '=', and '<') and outputs ('&')\nfunction getComponentBindings(name: string) {\n  const cmpDefs = <any[]>services.$injector.get(name + 'Directive'); // could be multiple\n  if (!cmpDefs || !cmpDefs.length) throw new Error(`Unable to find component named '${name}'`);\n  return cmpDefs.map(getBindings).reduce(unnestR, []);\n}\n\n// Given a directive definition, find its object input attributes\n// Use different properties, depending on the type of directive (component, bindToController, normal)\nconst getBindings = (def: any) => {\n  if (isObject(def.bindToController)) return scopeBindings(def.bindToController);\n  return scopeBindings(def.scope);\n};\n\ninterface BindingTuple {\n  name: string;\n  type: string;\n}\n\n// for ng 1.2 style, process the scope: { input: \"=foo\" }\n// for ng 1.3 through ng 1.5, process the component's bindToController: { input: \"=foo\" } object\nconst scopeBindings = (bindingsObj: Obj) =>\n  Object.keys(bindingsObj || {})\n    // [ 'input', [ '=foo', '=', 'foo' ] ]\n    .map((key) => [key, /^([=<@&])[?]?(.*)/.exec(bindingsObj[key])])\n    // skip malformed values\n    .filter((tuple) => isDefined(tuple) && isArray(tuple[1]))\n    // { name: ('foo' || 'input'), type: '=' }\n    .map((tuple) => ({ name: tuple[1][2] || tuple[0], type: tuple[1][1] } as BindingTuple));\n", "/** @publicapi @module ng1 */ /** */\nimport {\n  val,\n  isObject,\n  createProxyFunctions,\n  BuilderFunction,\n  StateRegistry,\n  StateService,\n  OnInvalidCallback,\n} from '@uirouter/core';\nimport { Ng1StateDeclaration } from './interface';\n\n/**\n * The Angular 1 `StateProvider`\n *\n * The `$stateProvider` works similar to Angular's v1 router, but it focuses purely\n * on state.\n *\n * A state corresponds to a \"place\" in the application in terms of the overall UI and\n * navigation. A state describes (via the controller / template / view properties) what\n * the UI looks like and does at that place.\n *\n * States often have things in common, and the primary way of factoring out these\n * commonalities in this model is via the state hierarchy, i.e. parent/child states aka\n * nested states.\n *\n * The `$stateProvider` provides interfaces to declare these states for your app.\n */\nexport class StateProvider {\n  constructor(private stateRegistry: StateRegistry, private stateService: StateService) {\n    createProxyFunctions(val(StateProvider.prototype), this, val(this));\n  }\n\n  /**\n   * Decorates states when they are registered\n   *\n   * Allows you to extend (carefully) or override (at your own peril) the\n   * `stateBuilder` object used internally by [[StateRegistry]].\n   * This can be used to add custom functionality to ui-router,\n   * for example inferring templateUrl based on the state name.\n   *\n   * When passing only a name, it returns the current (original or decorated) builder\n   * function that matches `name`.\n   *\n   * The builder functions that can be decorated are listed below. Though not all\n   * necessarily have a good use case for decoration, that is up to you to decide.\n   *\n   * In addition, users can attach custom decorators, which will generate new\n   * properties within the state's internal definition. There is currently no clear\n   * use-case for this beyond accessing internal states (i.e. $state.$current),\n   * however, expect this to become increasingly relevant as we introduce additional\n   * meta-programming features.\n   *\n   * **Warning**: Decorators should not be interdependent because the order of\n   * execution of the builder functions in non-deterministic. Builder functions\n   * should only be dependent on the state definition object and super function.\n   *\n   *\n   * Existing builder functions and current return values:\n   *\n   * - **parent** `{object}` - returns the parent state object.\n   * - **data** `{object}` - returns state data, including any inherited data that is not\n   *   overridden by own values (if any).\n   * - **url** `{object}` - returns a {@link ui.router.util.type:UrlMatcher UrlMatcher}\n   *   or `null`.\n   * - **navigable** `{object}` - returns closest ancestor state that has a URL (aka is\n   *   navigable).\n   * - **params** `{object}` - returns an array of state params that are ensured to\n   *   be a super-set of parent's params.\n   * - **views** `{object}` - returns a views object where each key is an absolute view\n   *   name (i.e. \"viewName@stateName\") and each value is the config object\n   *   (template, controller) for the view. Even when you don't use the views object\n   *   explicitly on a state config, one is still created for you internally.\n   *   So by decorating this builder function you have access to decorating template\n   *   and controller properties.\n   * - **ownParams** `{object}` - returns an array of params that belong to the state,\n   *   not including any params defined by ancestor states.\n   * - **path** `{string}` - returns the full path from the root down to this state.\n   *   Needed for state activation.\n   * - **includes** `{object}` - returns an object that includes every state that\n   *   would pass a `$state.includes()` test.\n   *\n   * #### Example:\n   * Override the internal 'views' builder with a function that takes the state\n   * definition, and a reference to the internal function being overridden:\n   * ```js\n   * $stateProvider.decorator('views', function (state, parent) {\n   *   let result = {},\n   *       views = parent(state);\n   *\n   *   angular.forEach(views, function (config, name) {\n   *     let autoName = (state.name + '.' + name).replace('.', '/');\n   *     config.templateUrl = config.templateUrl || '/partials/' + autoName + '.html';\n   *     result[name] = config;\n   *   });\n   *   return result;\n   * });\n   *\n   * $stateProvider.state('home', {\n   *   views: {\n   *     'contact.list': { controller: 'ListController' },\n   *     'contact.item': { controller: 'ItemController' }\n   *   }\n   * });\n   * ```\n   *\n   *\n   * ```js\n   * // Auto-populates list and item views with /partials/home/<USER>/list.html,\n   * // and /partials/home/<USER>/item.html, respectively.\n   * $state.go('home');\n   * ```\n   *\n   * @param {string} name The name of the builder function to decorate.\n   * @param {object} func A function that is responsible for decorating the original\n   * builder function. The function receives two parameters:\n   *\n   *   - `{object}` - state - The state config object.\n   *   - `{object}` - super - The original builder function.\n   *\n   * @return {object} $stateProvider - $stateProvider instance\n   */\n  decorator(name: string, func: BuilderFunction) {\n    return this.stateRegistry.decorator(name, func) || this;\n  }\n\n  /**\n   * Registers a state\n   *\n   * ### This is a passthrough to [[StateRegistry.register]].\n   *\n   * Registers a state configuration under a given state name.\n   * The stateConfig object has the following acceptable properties.\n   *\n   * <a id='template'></a>\n   *\n   * - **`template`** - {string|function=} - html template as a string or a function that returns\n   *   an html template as a string which should be used by the uiView directives. This property\n   *   takes precedence over templateUrl.\n   *\n   *   If `template` is a function, it will be called with the following parameters:\n   *\n   *   - {array.&lt;object&gt;} - state parameters extracted from the current $location.path() by\n   *     applying the current state\n   *\n   * <a id='templateUrl'></a>\n   *\n   * - **`templateUrl`** - {string|function=} - path or function that returns a path to an html\n   *   template that should be used by uiView.\n   *\n   *   If `templateUrl` is a function, it will be called with the following parameters:\n   *\n   *   - {array.&lt;object&gt;} - state parameters extracted from the current $location.path() by\n   *     applying the current state\n   *\n   * <a id='templateProvider'></a>\n   *\n   * - **`templateProvider`** - {function=} - Provider function that returns HTML content\n   *   string.\n   *\n   * <a id='controller'></a>\n   *\n   * - **`controller`** - {string|function=} -  Controller fn that should be associated with newly\n   *   related scope or the name of a registered controller if passed as a string.\n   *\n   * <a id='controllerProvider'></a>\n   *\n   * - **`controllerProvider`** - {function=} - Injectable provider function that returns\n   *   the actual controller or string.\n   *\n   * <a id='controllerAs'></a>\n   *\n   * - **`controllerAs`** – {string=} – A controller alias name. If present the controller will be\n   *   published to scope under the controllerAs name.\n   *\n   * <a id='resolve'></a>\n   *\n   * - **`resolve`** - {object.&lt;string, function&gt;=} - An optional map of dependencies which\n   *   should be injected into the controller. If any of these dependencies are promises,\n   *   the router will wait for them all to be resolved or one to be rejected before the\n   *   controller is instantiated. If all the promises are resolved successfully, the values\n   *   of the resolved promises are injected and $stateChangeSuccess event is fired. If any\n   *   of the promises are rejected the $stateChangeError event is fired. The map object is:\n   *\n   *   - key - {string}: name of dependency to be injected into controller\n   *   - factory - {string|function}: If string then it is alias for service. Otherwise if function,\n   *     it is injected and return value it treated as dependency. If result is a promise, it is\n   *     resolved before its value is injected into controller.\n   *\n   * <a id='url'></a>\n   *\n   * - **`url`** - {string=} - A url with optional parameters. When a state is navigated or\n   *   transitioned to, the `$stateParams` service will be populated with any\n   *   parameters that were passed.\n   *\n   * <a id='params'></a>\n   *\n   * - **`params`** - {object=} - An array of parameter names or regular expressions. Only\n   *   use this within a state if you are not using url. Otherwise you can specify your\n   *   parameters within the url. When a state is navigated or transitioned to, the\n   *   $stateParams service will be populated with any parameters that were passed.\n   *\n   * <a id='views'></a>\n   *\n   * - **`views`** - {object=} - Use the views property to set up multiple views or to target views\n   *   manually/explicitly.\n   *\n   * <a id='abstract'></a>\n   *\n   * - **`abstract`** - {boolean=} - An abstract state will never be directly activated,\n   *   but can provide inherited properties to its common children states.\n   *\n   * <a id='onEnter'></a>\n   *\n   * - **`onEnter`** - {object=} - Callback function for when a state is entered. Good way\n   *   to trigger an action or dispatch an event, such as opening a dialog.\n   * If minifying your scripts, make sure to use the `['injection1', 'injection2', function(injection1, injection2){}]` syntax.\n   *\n   * <a id='onExit'></a>\n   *\n   * - **`onExit`** - {object=} - Callback function for when a state is exited. Good way to\n   *   trigger an action or dispatch an event, such as opening a dialog.\n   * If minifying your scripts, make sure to use the `['injection1', 'injection2', function(injection1, injection2){}]` syntax.\n   *\n   * <a id='reloadOnSearch'></a>\n   *\n   * - **`reloadOnSearch = true`** - {boolean=} - If `false`, will not retrigger the same state\n   *   just because a search/query parameter has changed (via $location.search() or $location.hash()).\n   *   Useful for when you'd like to modify $location.search() without triggering a reload.\n   *\n   * <a id='data'></a>\n   *\n   * - **`data`** - {object=} - Arbitrary data object, useful for custom configuration.\n   *\n   * #### Example:\n   * Some state name examples\n   * ```js\n   * // stateName can be a single top-level name (must be unique).\n   * $stateProvider.state(\"home\", {});\n   *\n   * // Or it can be a nested state name. This state is a child of the\n   * // above \"home\" state.\n   * $stateProvider.state(\"home.newest\", {});\n   *\n   * // Nest states as deeply as needed.\n   * $stateProvider.state(\"home.newest.abc.xyz.inception\", {});\n   *\n   * // state() returns $stateProvider, so you can chain state declarations.\n   * $stateProvider\n   *   .state(\"home\", {})\n   *   .state(\"about\", {})\n   *   .state(\"contacts\", {});\n   * ```\n   *\n   * @param {string} name A unique state name, e.g. \"home\", \"about\", \"contacts\".\n   * To create a parent/child state use a dot, e.g. \"about.sales\", \"home.newest\".\n   * @param {object} definition State configuration object.\n   */\n  state(name: string, definition: Ng1StateDeclaration): StateProvider;\n  state(definition: Ng1StateDeclaration): StateProvider;\n  state(name: any, definition?: any) {\n    if (isObject(name)) {\n      definition = name;\n    } else {\n      definition.name = name;\n    }\n    this.stateRegistry.register(definition);\n    return this;\n  }\n\n  /**\n   * Registers an invalid state handler\n   *\n   * This is a passthrough to [[StateService.onInvalid]] for ng1.\n   */\n\n  onInvalid(callback: OnInvalidCallback): Function {\n    return this.stateService.onInvalid(callback);\n  }\n}\n", "/** @publicapi @module ng1 */ /** */\nimport {\n  StateObject,\n  TransitionStateHookFn,\n  HookResult,\n  Transition,\n  services,\n  ResolveContext,\n  extend,\n} from '@uirouter/core';\nimport { getLocals } from '../services';\nimport { Ng1StateDeclaration } from '../interface';\n\n/**\n * This is a [[StateBuilder.builder]] function for angular1 `onEnter`, `onExit`,\n * `onRetain` callback hooks on a [[Ng1StateDeclaration]].\n *\n * When the [[StateBuilder]] builds a [[StateObject]] object from a raw [[StateDeclaration]], this builder\n * ensures that those hooks are injectable for @uirouter/angularjs (ng1).\n *\n * @internalapi\n */\nexport const getStateHookBuilder = (hookName: 'onEnter' | 'onExit' | 'onRetain') =>\n  function stateHookBuilder(stateObject: StateObject): TransitionStateHookFn {\n    const hook = stateObject[hookName];\n    const pathname = hookName === 'onExit' ? 'from' : 'to';\n\n    function decoratedNg1Hook(trans: Transition, state: Ng1StateDeclaration): HookResult {\n      const resolveContext = new ResolveContext(trans.treeChanges(pathname));\n      const subContext = resolveContext.subContext(state.$$state());\n      const locals = extend(getLocals(subContext), { $state$: state, $transition$: trans });\n      return services.$injector.invoke(hook, this, locals);\n    }\n\n    return hook ? decoratedNg1Hook : undefined;\n  };\n", "/** @publicapi @module ng1 */ /** */\nimport { LocationConfig, LocationServices, UIRouter, ParamType, isDefined } from '@uirouter/core';\nimport { val, createProxyFunctions, removeFrom, isObject } from '@uirouter/core';\nimport { ILocationService, ILocationProvider, IWindowService } from 'angular';\n\n/**\n * Implements UI-Router LocationServices and LocationConfig using Angular 1's $location service\n * @internalapi\n */\nexport class Ng1LocationServices implements LocationConfig, LocationServices {\n  private $locationProvider: ILocationProvider;\n  private $location: ILocationService;\n  private $sniffer: any;\n  private $browser: any;\n  private $window: IWindowService;\n\n  path;\n  search;\n  hash;\n  hashPrefix;\n  port;\n  protocol;\n  host;\n\n  private _baseHref: string;\n\n  // .onChange() registry\n  private _urlListeners: Function[] = [];\n\n  /**\n   * Applys ng1-specific path parameter encoding\n   *\n   * The Angular 1 `$location` service is a bit weird.\n   * It doesn't allow slashes to be encoded/decoded bi-directionally.\n   *\n   * See the writeup at https://github.com/angular-ui/ui-router/issues/2598\n   *\n   * This code patches the `path` parameter type so it encoded/decodes slashes as ~2F\n   *\n   * @param router\n   */\n  static monkeyPatchPathParameterType(router: UIRouter) {\n    const pathType: ParamType = router.urlMatcherFactory.type('path');\n\n    pathType.encode = (x: any) =>\n      x != null ? x.toString().replace(/(~|\\/)/g, (m) => ({ '~': '~~', '/': '~2F' }[m])) : x;\n\n    pathType.decode = (x: string) =>\n      x != null ? x.toString().replace(/(~~|~2F)/g, (m) => ({ '~~': '~', '~2F': '/' }[m])) : x;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  dispose() {}\n\n  constructor($locationProvider: ILocationProvider) {\n    this.$locationProvider = $locationProvider;\n    const _lp = val($locationProvider);\n    createProxyFunctions(_lp, this, _lp, ['hashPrefix']);\n  }\n\n  onChange(callback: Function) {\n    this._urlListeners.push(callback);\n    return () => removeFrom(this._urlListeners)(callback);\n  }\n\n  html5Mode() {\n    let html5Mode: any = this.$locationProvider.html5Mode();\n    html5Mode = isObject(html5Mode) ? html5Mode.enabled : html5Mode;\n    return html5Mode && this.$sniffer.history;\n  }\n\n  baseHref() {\n    return this._baseHref || (this._baseHref = this.$browser.baseHref() || this.$window.location.pathname);\n  }\n\n  url(newUrl?: string, replace = false, state?) {\n    if (isDefined(newUrl)) this.$location.url(newUrl);\n    if (replace) this.$location.replace();\n    if (state) this.$location.state(state);\n    return this.$location.url();\n  }\n\n  _runtimeServices($rootScope, $location: ILocationService, $sniffer, $browser, $window: IWindowService) {\n    this.$location = $location;\n    this.$sniffer = $sniffer;\n    this.$browser = $browser;\n    this.$window = $window;\n\n    // Bind $locationChangeSuccess to the listeners registered in LocationService.onChange\n    $rootScope.$on('$locationChangeSuccess', (evt) => this._urlListeners.forEach((fn) => fn(evt)));\n    const _loc = val($location);\n\n    // Bind these LocationService functions to $location\n    createProxyFunctions(_loc, this, _loc, ['replace', 'path', 'search', 'hash']);\n    // Bind these LocationConfig functions to $location\n    createProxyFunctions(_loc, this, _loc, ['port', 'protocol', 'host']);\n  }\n}\n", "/** @publicapi @module url */ /** */\nimport {\n  UIRouter,\n  LocationServices,\n  $InjectorLike,\n  BaseUrlRule,\n  UrlRuleHandlerFn,\n  UrlMatcher,\n  IInjectable,\n  UrlRouter,\n} from '@uirouter/core';\nimport { services, isString, isFunction, isArray, identity } from '@uirouter/core';\n\nexport interface RawNg1RuleFunction {\n  ($injector: $InjectorLike, $location: LocationServices): string | void;\n}\n\n/**\n * Manages rules for client-side URL\n *\n * ### Deprecation warning:\n * This class is now considered to be an internal API\n * Use the [[UrlService]] instead.\n * For configuring URL rules, use the [[UrlRulesApi]] which can be found as [[UrlService.rules]].\n *\n * This class manages the router rules for what to do when the URL changes.\n *\n * This provider remains for backwards compatibility.\n *\n * @internalapi\n * @deprecated\n */\nexport class UrlRouterProvider {\n  static injectableHandler(router: UIRouter, handler: IInjectable): UrlRuleHandlerFn {\n    return (match) => services.$injector.invoke(handler, null, { $match: match, $stateParams: router.globals.params });\n  }\n\n  /** @hidden */\n  constructor(/** @hidden */ private router: UIRouter) {}\n\n  /** @hidden */\n  $get(): UrlRouter {\n    const urlService = this.router.urlService;\n    this.router.urlRouter.update(true);\n    if (!urlService.interceptDeferred) urlService.listen();\n    return this.router.urlRouter;\n  }\n\n  /**\n   * Registers a url handler function.\n   *\n   * Registers a low level url handler (a `rule`).\n   * A rule detects specific URL patterns and returns a redirect, or performs some action.\n   *\n   * If a rule returns a string, the URL is replaced with the string, and all rules are fired again.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   // Here's an example of how you might allow case insensitive urls\n   *   $urlRouterProvider.rule(function ($injector, $location) {\n   *     var path = $location.path(),\n   *         normalized = path.toLowerCase();\n   *\n   *     if (path !== normalized) {\n   *       return normalized;\n   *     }\n   *   });\n   * });\n   * ```\n   *\n   * @param ruleFn\n   * Handler function that takes `$injector` and `$location` services as arguments.\n   * You can use them to detect a url and return a different url as a string.\n   *\n   * @return [[UrlRouterProvider]] (`this`)\n   */\n  rule(ruleFn: RawNg1RuleFunction): UrlRouterProvider {\n    if (!isFunction(ruleFn)) throw new Error(\"'rule' must be a function\");\n\n    const match = () => ruleFn(services.$injector, this.router.locationService);\n\n    const rule = new BaseUrlRule(match, identity);\n    this.router.urlService.rules.rule(rule);\n    return this;\n  }\n\n  /**\n   * Defines the path or behavior to use when no url can be matched.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   // if the path doesn't match any of the urls you configured\n   *   // otherwise will take care of routing the user to the\n   *   // specified url\n   *   $urlRouterProvider.otherwise('/index');\n   *\n   *   // Example of using function rule as param\n   *   $urlRouterProvider.otherwise(function ($injector, $location) {\n   *     return '/a/valid/url';\n   *   });\n   * });\n   * ```\n   *\n   * @param rule\n   * The url path you want to redirect to or a function rule that returns the url path or performs a `$state.go()`.\n   * The function version is passed two params: `$injector` and `$location` services, and should return a url string.\n   *\n   * @return {object} `$urlRouterProvider` - `$urlRouterProvider` instance\n   */\n  otherwise(rule: string | RawNg1RuleFunction): UrlRouterProvider {\n    const urlRules = this.router.urlService.rules;\n    if (isString(rule)) {\n      urlRules.otherwise(rule);\n    } else if (isFunction(rule)) {\n      urlRules.otherwise(() => rule(services.$injector, this.router.locationService));\n    } else {\n      throw new Error(\"'rule' must be a string or function\");\n    }\n\n    return this;\n  }\n\n  /**\n   * Registers a handler for a given url matching.\n   *\n   * If the handler is a string, it is\n   * treated as a redirect, and is interpolated according to the syntax of match\n   * (i.e. like `String.replace()` for `RegExp`, or like a `UrlMatcher` pattern otherwise).\n   *\n   * If the handler is a function, it is injectable.\n   * It gets invoked if `$location` matches.\n   * You have the option of inject the match object as `$match`.\n   *\n   * The handler can return\n   *\n   * - **falsy** to indicate that the rule didn't match after all, then `$urlRouter`\n   *   will continue trying to find another one that matches.\n   * - **string** which is treated as a redirect and passed to `$location.url()`\n   * - **void** or any **truthy** value tells `$urlRouter` that the url was handled.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   $urlRouterProvider.when($state.url, function ($match, $stateParams) {\n   *     if ($state.$current.navigable !== state ||\n   *         !equalForKeys($match, $stateParams) {\n   *      $state.transitionTo(state, $match, false);\n   *     }\n   *   });\n   * });\n   * ```\n   *\n   * @param what A pattern string to match, compiled as a [[UrlMatcher]].\n   * @param handler The path (or function that returns a path) that you want to redirect your user to.\n   * @param ruleCallback [optional] A callback that receives the `rule` registered with [[UrlMatcher.rule]]\n   *\n   * Note: the handler may also invoke arbitrary code, such as `$state.go()`\n   */\n  when(what: RegExp | UrlMatcher | string, handler: string | IInjectable): this {\n    if (isArray(handler) || isFunction(handler)) {\n      handler = UrlRouterProvider.injectableHandler(this.router, handler);\n    }\n\n    this.router.urlService.rules.when(what, handler as any);\n    return this;\n  }\n\n  /**\n   * Disables monitoring of the URL.\n   *\n   * Call this method before UI-Router has bootstrapped.\n   * It will stop UI-Router from performing the initial url sync.\n   *\n   * This can be useful to perform some asynchronous initialization before the router starts.\n   * Once the initialization is complete, call [[listen]] to tell UI-Router to start watching and synchronizing the URL.\n   *\n   * #### Example:\n   * ```js\n   * var app = angular.module('app', ['ui.router']);\n   *\n   * app.config(function ($urlRouterProvider) {\n   *   // Prevent $urlRouter from automatically intercepting URL changes;\n   *   $urlRouterProvider.deferIntercept();\n   * })\n   *\n   * app.run(function (MyService, $urlRouter, $http) {\n   *   $http.get(\"/stuff\").then(function(resp) {\n   *     MyService.doStuff(resp.data);\n   *     $urlRouter.listen();\n   *     $urlRouter.sync();\n   *   });\n   * });\n   * ```\n   *\n   * @param defer Indicates whether to defer location change interception.\n   *        Passing no parameter is equivalent to `true`.\n   */\n  deferIntercept(defer?: boolean): void {\n    this.router.urlService.deferIntercept(defer);\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-empty-function */\n/* eslint-disable @typescript-eslint/no-unused-vars */\n/**\n * # Angular 1 types\n *\n * UI-Router core provides various Typescript types which you can use for code completion and validating parameter values, etc.\n * The customizations to the core types for Angular UI-Router are documented here.\n *\n * The optional [[$resolve]] service is also documented here.\n *\n * @preferred @publicapi @module ng1\n */ /** */\nimport { ng as angular } from './angular';\nimport {\n  IRootScopeService,\n  IQService,\n  ILocationService,\n  ILocationProvider,\n  IHttpService,\n  ITemplateCacheService,\n} from 'angular';\nimport {\n  services,\n  applyPairs,\n  isString,\n  trace,\n  extend,\n  UIRouter,\n  StateService,\n  UrlRouter,\n  UrlMatcherFactory,\n  ResolveContext,\n  unnestR,\n  TypedMap,\n} from '@uirouter/core';\nimport { ng1ViewsBuilder, getNg1ViewConfigFactory } from './statebuilders/views';\nimport { TemplateFactory } from './templateFactory';\nimport { StateProvider } from './stateProvider';\nimport { getStateHookBuilder } from './statebuilders/onEnterExitRetain';\nimport { Ng1LocationServices } from './locationServices';\nimport { UrlRouterProvider } from './urlRouterProvider';\nimport IInjectorService = angular.auto.IInjectorService;\n\nangular.module('ui.router.angular1', []);\nconst mod_init = angular.module('ui.router.init', ['ng']);\nconst mod_util = angular.module('ui.router.util', ['ui.router.init']);\nconst mod_rtr = angular.module('ui.router.router', ['ui.router.util']);\nconst mod_state = angular.module('ui.router.state', ['ui.router.router', 'ui.router.util', 'ui.router.angular1']);\nconst mod_main = angular.module('ui.router', ['ui.router.init', 'ui.router.state', 'ui.router.angular1']);\nconst mod_cmpt = angular.module('ui.router.compat', ['ui.router']);\n\ndeclare module '@uirouter/core/lib/router' {\n  interface UIRouter {\n    /** @hidden */\n    stateProvider: StateProvider;\n    /** @hidden */\n    urlRouterProvider: UrlRouterProvider;\n  }\n}\n\nlet router: UIRouter = null;\n\n$uiRouterProvider.$inject = ['$locationProvider'];\n/** This angular 1 provider instantiates a Router and exposes its services via the angular injector */\nfunction $uiRouterProvider($locationProvider: ILocationProvider) {\n  // Create a new instance of the Router when the $uiRouterProvider is initialized\n  router = this.router = new UIRouter();\n  router.stateProvider = new StateProvider(router.stateRegistry, router.stateService);\n\n  // Apply ng1 specific StateBuilder code for `views`, `resolve`, and `onExit/Retain/Enter` properties\n  router.stateRegistry.decorator('views', ng1ViewsBuilder);\n  router.stateRegistry.decorator('onExit', getStateHookBuilder('onExit'));\n  router.stateRegistry.decorator('onRetain', getStateHookBuilder('onRetain'));\n  router.stateRegistry.decorator('onEnter', getStateHookBuilder('onEnter'));\n\n  router.viewService._pluginapi._viewConfigFactory('ng1', getNg1ViewConfigFactory());\n\n  // Disable decoding of params by UrlMatcherFactory because $location already handles this\n  router.urlService.config._decodeParams = false;\n\n  const ng1LocationService = (router.locationService = router.locationConfig = new Ng1LocationServices(\n    $locationProvider\n  ));\n\n  Ng1LocationServices.monkeyPatchPathParameterType(router);\n\n  // backwards compat: also expose router instance as $uiRouterProvider.router\n  router['router'] = router;\n  router['$get'] = $get;\n  $get.$inject = ['$location', '$browser', '$window', '$sniffer', '$rootScope', '$http', '$templateCache'];\n  function $get(\n    $location: ILocationService,\n    $browser: any,\n    $window: any,\n    $sniffer: any,\n    $rootScope: ng.IScope,\n    $http: IHttpService,\n    $templateCache: ITemplateCacheService\n  ) {\n    ng1LocationService._runtimeServices($rootScope, $location, $sniffer, $browser, $window);\n    delete router['router'];\n    delete router['$get'];\n    return router;\n  }\n  return router;\n}\n\nconst getProviderFor = (serviceName) => [\n  '$uiRouterProvider',\n  ($urp) => {\n    const service = $urp.router[serviceName];\n    service['$get'] = () => service;\n    return service;\n  },\n];\n\n// This effectively calls $get() on `$uiRouterProvider` to trigger init (when ng enters runtime)\nrunBlock.$inject = ['$injector', '$q', '$uiRouter'];\nfunction runBlock($injector: IInjectorService, $q: IQService, $uiRouter: UIRouter) {\n  services.$injector = $injector;\n  services.$q = <any>$q;\n\n  // https://github.com/angular-ui/ui-router/issues/3678\n  if (!Object.prototype.hasOwnProperty.call($injector, 'strictDi')) {\n    try {\n      $injector.invoke(function (checkStrictDi) {});\n    } catch (error) {\n      $injector.strictDi = !!/strict mode/.exec(error && error.toString());\n    }\n  }\n\n  // The $injector is now available.\n  // Find any resolvables that had dependency annotation deferred\n  $uiRouter.stateRegistry\n    .get()\n    .map((x) => x.$$state().resolvables)\n    .reduce(unnestR, [])\n    .filter((x) => x.deps === 'deferred')\n    .forEach((resolvable) => (resolvable.deps = $injector.annotate(resolvable.resolveFn, $injector.strictDi)));\n}\n\n// $urlRouter service and $urlRouterProvider\nconst getUrlRouterProvider = (uiRouter: UIRouter) => (uiRouter.urlRouterProvider = new UrlRouterProvider(uiRouter));\n\n// $state service and $stateProvider\n// $urlRouter service and $urlRouterProvider\nconst getStateProvider = () => extend(router.stateProvider, { $get: () => router.stateService });\n\nwatchDigests.$inject = ['$rootScope'];\nexport function watchDigests($rootScope: IRootScopeService) {\n  $rootScope.$watch(function () {\n    trace.approximateDigests++;\n  });\n}\n\nmod_init.provider('$uiRouter', <any>$uiRouterProvider);\nmod_rtr.provider('$urlRouter', ['$uiRouterProvider', getUrlRouterProvider]);\nmod_util.provider('$urlService', getProviderFor('urlService'));\nmod_util.provider('$urlMatcherFactory', ['$uiRouterProvider', () => router.urlMatcherFactory]);\nmod_util.provider('$templateFactory', () => new TemplateFactory());\nmod_state.provider('$stateRegistry', getProviderFor('stateRegistry'));\nmod_state.provider('$uiRouterGlobals', getProviderFor('globals'));\nmod_state.provider('$transitions', getProviderFor('transitionService'));\nmod_state.provider('$state', ['$uiRouterProvider', getStateProvider]);\n\nmod_state.factory('$stateParams', ['$uiRouter', ($uiRouter: UIRouter) => $uiRouter.globals.params]);\nmod_main.factory('$view', () => router.viewService);\nmod_main.service('$trace', () => trace);\n\nmod_main.run(watchDigests);\nmod_util.run(['$urlMatcherFactory', function ($urlMatcherFactory: UrlMatcherFactory) {}]);\nmod_state.run(['$state', function ($state: StateService) {}]);\nmod_rtr.run(['$urlRouter', function ($urlRouter: UrlRouter) {}]);\nmod_init.run(runBlock);\n\n/** @hidden TODO: find a place to move this */\nexport const getLocals = (ctx: ResolveContext): TypedMap<any> => {\n  const tokens = ctx.getTokens().filter(isString);\n\n  const tuples = tokens.map((key) => {\n    const resolvable = ctx.getResolvable(key);\n    const waitPolicy = ctx.getPolicy(resolvable).async;\n    return [key, waitPolicy === 'NOWAIT' ? resolvable.promise : resolvable.data];\n  });\n\n  return tuples.reduce(applyPairs, {});\n};\n", "/* eslint-disable @typescript-eslint/no-empty-interface */\n/* eslint-disable prefer-const */\n/**\n * # Angular 1 Directives\n *\n * These are the directives included in UI-Router for Angular 1.\n * These directives are used in templates to create viewports and link/navigate to states.\n *\n * @preferred @publicapi @module directives\n */ /** */\nimport { ng as angular } from '../angular';\nimport { IAugmentedJQuery, ITimeoutService, IScope, IInterpolateService } from 'angular';\n\nimport {\n  Obj,\n  extend,\n  forEach,\n  tail,\n  isString,\n  isObject,\n  isArray,\n  parse,\n  noop,\n  unnestR,\n  identity,\n  uniqR,\n  inArray,\n  removeFrom,\n  RawParams,\n  PathNode,\n  StateOrName,\n  StateService,\n  StateDeclaration,\n  UIRouter,\n} from '@uirouter/core';\nimport { UIViewData } from './viewDirective';\n\n/** @hidden Used for typedoc */\nexport interface ng1_directive {}\n\n/** @hidden */\nfunction parseStateRef(ref: string) {\n  const paramsOnly = ref.match(/^\\s*({[^}]*})\\s*$/);\n  if (paramsOnly) ref = '(' + paramsOnly[1] + ')';\n\n  const parsed = ref.replace(/\\n/g, ' ').match(/^\\s*([^(]*?)\\s*(\\((.*)\\))?\\s*$/);\n  if (!parsed || parsed.length !== 4) throw new Error(\"Invalid state ref '\" + ref + \"'\");\n  return { state: parsed[1] || null, paramExpr: parsed[3] || null };\n}\n\n/** @hidden */\nfunction stateContext(el: IAugmentedJQuery) {\n  const $uiView: UIViewData = (el.parent() as IAugmentedJQuery).inheritedData('$uiView');\n  const path: PathNode[] = parse('$cfg.path')($uiView);\n  return path ? tail(path).state.name : undefined;\n}\n\n/** @hidden */\nfunction processedDef($state: StateService, $element: IAugmentedJQuery, def: Def): Def {\n  const uiState = def.uiState || $state.current.name;\n  const uiStateOpts = extend(defaultOpts($element, $state), def.uiStateOpts || {});\n  const href = $state.href(uiState, def.uiStateParams, uiStateOpts);\n  return { uiState, uiStateParams: def.uiStateParams, uiStateOpts, href };\n}\n\n/** @hidden */\ninterface TypeInfo {\n  attr: string;\n  isAnchor: boolean;\n  clickable: boolean;\n}\n\n/** @hidden */\nfunction getTypeInfo(el: IAugmentedJQuery): TypeInfo {\n  // SVGAElement does not use the href attribute, but rather the 'xlinkHref' attribute.\n  const isSvg = Object.prototype.toString.call(el.prop('href')) === '[object SVGAnimatedString]';\n  const isForm = el[0].nodeName === 'FORM';\n\n  return {\n    attr: isForm ? 'action' : isSvg ? 'xlink:href' : 'href',\n    isAnchor: el.prop('tagName').toUpperCase() === 'A',\n    clickable: !isForm,\n  };\n}\n\n/** @hidden */\nfunction clickHook(\n  el: IAugmentedJQuery,\n  $state: StateService,\n  $timeout: ITimeoutService,\n  type: TypeInfo,\n  getDef: () => Def\n) {\n  return function (e: JQueryMouseEventObject) {\n    const button = e.which || e.button,\n      target = getDef();\n\n    if (!(button > 1 || e.ctrlKey || e.metaKey || e.shiftKey || e.altKey || el.attr('target'))) {\n      // HACK: This is to allow ng-clicks to be processed before the transition is initiated:\n      const transition = $timeout(function () {\n        if (!el.attr('disabled')) {\n          $state.go(target.uiState, target.uiStateParams, target.uiStateOpts);\n        }\n      });\n      e.preventDefault();\n\n      // if the state has no URL, ignore one preventDefault from the <a> directive.\n      let ignorePreventDefaultCount = type.isAnchor && !target.href ? 1 : 0;\n\n      e.preventDefault = function () {\n        if (ignorePreventDefaultCount-- <= 0) $timeout.cancel(transition);\n      };\n    }\n  };\n}\n\n/** @hidden */\nfunction defaultOpts(el: IAugmentedJQuery, $state: StateService) {\n  return {\n    relative: stateContext(el) || $state.$current,\n    inherit: true,\n    source: 'sref',\n  };\n}\n\n/** @hidden */\nfunction bindEvents(element: IAugmentedJQuery, scope: IScope, hookFn: EventListener, uiStateOpts: any): void {\n  let events;\n\n  if (uiStateOpts) {\n    events = uiStateOpts.events;\n  }\n\n  if (!isArray(events)) {\n    events = ['click'];\n  }\n\n  const on = element.on ? 'on' : 'bind';\n  for (const event of events) {\n    element[on](event, hookFn);\n  }\n\n  scope.$on('$destroy', function () {\n    const off = element.off ? 'off' : 'unbind';\n    for (const event of events) {\n      element[off](event, hookFn as any);\n    }\n  });\n}\n\n/**\n * `ui-sref`: A directive for linking to a state\n *\n * A directive which links to a state (and optionally, parameters).\n * When clicked, this directive activates the linked state with the supplied parameter values.\n *\n * ### Linked State\n * The attribute value of the `ui-sref` is the name of the state to link to.\n *\n * #### Example:\n * This will activate the `home` state when the link is clicked.\n * ```html\n * <a ui-sref=\"home\">Home</a>\n * ```\n *\n * ### Relative Links\n * You can also use relative state paths within `ui-sref`, just like a relative path passed to `$state.go()` ([[StateService.go]]).\n * You just need to be aware that the path is relative to the state that *created* the link.\n * This allows a state to create a relative `ui-sref` which always targets the same destination.\n *\n * #### Example:\n * Both these links are relative to the parent state, even when a child state is currently active.\n * ```html\n * <a ui-sref=\".child1\">child 1 state</a>\n * <a ui-sref=\".child2\">child 2 state</a>\n * ```\n *\n * This link activates the parent state.\n * ```html\n * <a ui-sref=\"^\">Return</a>\n * ```\n *\n * ### hrefs\n * If the linked state has a URL, the directive will automatically generate and\n * update the `href` attribute (using the [[StateService.href]]  method).\n *\n * #### Example:\n * Assuming the `users` state has a url of `/users/`\n * ```html\n * <a ui-sref=\"users\" href=\"/users/\">Users</a>\n * ```\n *\n * ### Parameter Values\n * In addition to the state name, a `ui-sref` can include parameter values which are applied when activating the state.\n * Param values can be provided in the `ui-sref` value after the state name, enclosed by parentheses.\n * The content inside the parentheses is an expression, evaluated to the parameter values.\n *\n * #### Example:\n * This example renders a list of links to users.\n * The state's `userId` parameter value comes from each user's `user.id` property.\n * ```html\n * <li ng-repeat=\"user in users\">\n *   <a ui-sref=\"users.detail({ userId: user.id })\">{{ user.displayName }}</a>\n * </li>\n * ```\n *\n * Note:\n * The parameter values expression is `$watch`ed for updates.\n *\n * ### Transition Options\n * You can specify [[TransitionOptions]] to pass to [[StateService.go]] by using the `ui-sref-opts` attribute.\n * Options are restricted to `location`, `inherit`, and `reload`.\n *\n * #### Example:\n * ```html\n * <a ui-sref=\"home\" ui-sref-opts=\"{ reload: true }\">Home</a>\n * ```\n *\n * ### Other DOM Events\n *\n * You can also customize which DOM events to respond to (instead of `click`) by\n * providing an `events` array in the `ui-sref-opts` attribute.\n *\n * #### Example:\n * ```html\n * <input type=\"text\" ui-sref=\"contacts\" ui-sref-opts=\"{ events: ['change', 'blur'] }\">\n * ```\n *\n * ### Highlighting the active link\n * This directive can be used in conjunction with [[uiSrefActive]] to highlight the active link.\n *\n * ### Examples\n * If you have the following template:\n *\n * ```html\n * <a ui-sref=\"home\">Home</a>\n * <a ui-sref=\"about\">About</a>\n * <a ui-sref=\"{page: 2}\">Next page</a>\n *\n * <ul>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a ui-sref=\"contacts.detail({ id: contact.id })\">{{ contact.name }}</a>\n *     </li>\n * </ul>\n * ```\n *\n * Then (assuming the current state is `contacts`) the rendered html including hrefs would be:\n *\n * ```html\n * <a href=\"#/home\" ui-sref=\"home\">Home</a>\n * <a href=\"#/about\" ui-sref=\"about\">About</a>\n * <a href=\"#/contacts?page=2\" ui-sref=\"{page: 2}\">Next page</a>\n *\n * <ul>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a href=\"#/contacts/1\" ui-sref=\"contacts.detail({ id: contact.id })\">Joe</a>\n *     </li>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a href=\"#/contacts/2\" ui-sref=\"contacts.detail({ id: contact.id })\">Alice</a>\n *     </li>\n *     <li ng-repeat=\"contact in contacts\">\n *         <a href=\"#/contacts/3\" ui-sref=\"contacts.detail({ id: contact.id })\">Bob</a>\n *     </li>\n * </ul>\n *\n * <a href=\"#/home\" ui-sref=\"home\" ui-sref-opts=\"{reload: true}\">Home</a>\n * ```\n *\n * ### Notes\n *\n * - You can use `ui-sref` to change **only the parameter values** by omitting the state name and parentheses.\n * #### Example:\n * Sets the `lang` parameter to `en` and remains on the same state.\n *\n * ```html\n * <a ui-sref=\"{ lang: 'en' }\">English</a>\n * ```\n *\n * - A middle-click, right-click, or ctrl-click is handled (natively) by the browser to open the href in a new window, for example.\n *\n * - Unlike the parameter values expression, the state name is not `$watch`ed (for performance reasons).\n * If you need to dynamically update the state being linked to, use the fully dynamic [[uiState]] directive.\n */\nlet uiSrefDirective: ng1_directive;\nuiSrefDirective = [\n  '$uiRouter',\n  '$timeout',\n  function $StateRefDirective($uiRouter: UIRouter, $timeout: ITimeoutService) {\n    const $state = $uiRouter.stateService;\n\n    return {\n      restrict: 'A',\n      require: ['?^uiSrefActive', '?^uiSrefActiveEq'],\n      link: function (scope: IScope, element: IAugmentedJQuery, attrs: any, uiSrefActive: any) {\n        const type = getTypeInfo(element);\n        const active = uiSrefActive[1] || uiSrefActive[0];\n        let unlinkInfoFn: Function = null;\n\n        const rawDef = {} as Def;\n        const getDef = () => processedDef($state, element, rawDef);\n\n        const ref = parseStateRef(attrs.uiSref);\n        rawDef.uiState = ref.state;\n        rawDef.uiStateOpts = attrs.uiSrefOpts ? scope.$eval(attrs.uiSrefOpts) : {};\n\n        function update() {\n          const def = getDef();\n          if (unlinkInfoFn) unlinkInfoFn();\n          if (active) unlinkInfoFn = active.$$addStateInfo(def.uiState, def.uiStateParams);\n          if (def.href != null) attrs.$set(type.attr, def.href);\n        }\n\n        if (ref.paramExpr) {\n          scope.$watch(\n            ref.paramExpr,\n            function (val) {\n              rawDef.uiStateParams = extend({}, val);\n              update();\n            },\n            true\n          );\n          rawDef.uiStateParams = extend({}, scope.$eval(ref.paramExpr));\n        }\n\n        update();\n\n        scope.$on('$destroy', <any>$uiRouter.stateRegistry.onStatesChanged(update));\n        scope.$on('$destroy', <any>$uiRouter.transitionService.onSuccess({}, update));\n\n        if (!type.clickable) return;\n        const hookFn = clickHook(element, $state, $timeout, type, getDef);\n        bindEvents(element, scope, hookFn, rawDef.uiStateOpts);\n      },\n    };\n  },\n];\n\n/**\n * `ui-state`: A fully dynamic directive for linking to a state\n *\n * A directive which links to a state (and optionally, parameters).\n * When clicked, this directive activates the linked state with the supplied parameter values.\n *\n * **This directive is very similar to [[uiSref]], but it `$observe`s and `$watch`es/evaluates all its inputs.**\n *\n * A directive which links to a state (and optionally, parameters).\n * When clicked, this directive activates the linked state with the supplied parameter values.\n *\n * ### Linked State\n * The attribute value of `ui-state` is an expression which is `$watch`ed and evaluated as the state to link to.\n * **This is in contrast with `ui-sref`, which takes a state name as a string literal.**\n *\n * #### Example:\n * Create a list of links.\n * ```html\n * <li ng-repeat=\"link in navlinks\">\n *   <a ui-state=\"link.state\">{{ link.displayName }}</a>\n * </li>\n * ```\n *\n * ### Relative Links\n * If the expression evaluates to a relative path, it is processed like [[uiSref]].\n * You just need to be aware that the path is relative to the state that *created* the link.\n * This allows a state to create relative `ui-state` which always targets the same destination.\n *\n * ### hrefs\n * If the linked state has a URL, the directive will automatically generate and\n * update the `href` attribute (using the [[StateService.href]]  method).\n *\n * ### Parameter Values\n * In addition to the state name expression, a `ui-state` can include parameter values which are applied when activating the state.\n * Param values should be provided using the `ui-state-params` attribute.\n * The `ui-state-params` attribute value is `$watch`ed and evaluated as an expression.\n *\n * #### Example:\n * This example renders a list of links with param values.\n * The state's `userId` parameter value comes from each user's `user.id` property.\n * ```html\n * <li ng-repeat=\"link in navlinks\">\n *   <a ui-state=\"link.state\" ui-state-params=\"link.params\">{{ link.displayName }}</a>\n * </li>\n * ```\n *\n * ### Transition Options\n * You can specify [[TransitionOptions]] to pass to [[StateService.go]] by using the `ui-state-opts` attribute.\n * Options are restricted to `location`, `inherit`, and `reload`.\n * The value of the `ui-state-opts` is `$watch`ed and evaluated as an expression.\n *\n * #### Example:\n * ```html\n * <a ui-state=\"returnto.state\" ui-state-opts=\"{ reload: true }\">Home</a>\n * ```\n *\n * ### Other DOM Events\n *\n * You can also customize which DOM events to respond to (instead of `click`) by\n * providing an `events` array in the `ui-state-opts` attribute.\n *\n * #### Example:\n * ```html\n * <input type=\"text\" ui-state=\"contacts\" ui-state-opts=\"{ events: ['change', 'blur'] }\">\n * ```\n *\n * ### Highlighting the active link\n * This directive can be used in conjunction with [[uiSrefActive]] to highlight the active link.\n *\n * ### Notes\n *\n * - You can use `ui-params` to change **only the parameter values** by omitting the state name and supplying only `ui-state-params`.\n *   However, it might be simpler to use [[uiSref]] parameter-only links.\n *\n * #### Example:\n * Sets the `lang` parameter to `en` and remains on the same state.\n *\n * ```html\n * <a ui-state=\"\" ui-state-params=\"{ lang: 'en' }\">English</a>\n * ```\n *\n * - A middle-click, right-click, or ctrl-click is handled (natively) by the browser to open the href in a new window, for example.\n * ```\n */\nlet uiStateDirective: ng1_directive;\nuiStateDirective = [\n  '$uiRouter',\n  '$timeout',\n  function $StateRefDynamicDirective($uiRouter: UIRouter, $timeout: ITimeoutService) {\n    const $state = $uiRouter.stateService;\n\n    return {\n      restrict: 'A',\n      require: ['?^uiSrefActive', '?^uiSrefActiveEq'],\n      link: function (scope: IScope, element: IAugmentedJQuery, attrs: any, uiSrefActive: any) {\n        const type = getTypeInfo(element);\n        const active = uiSrefActive[1] || uiSrefActive[0];\n        let unlinkInfoFn: Function = null;\n        let hookFn;\n\n        const rawDef = {} as Def;\n        const getDef = () => processedDef($state, element, rawDef);\n\n        const inputAttrs = ['uiState', 'uiStateParams', 'uiStateOpts'];\n        const watchDeregFns = inputAttrs.reduce((acc, attr) => ((acc[attr] = noop), acc), {});\n\n        function update() {\n          const def = getDef();\n          if (unlinkInfoFn) unlinkInfoFn();\n          if (active) unlinkInfoFn = active.$$addStateInfo(def.uiState, def.uiStateParams);\n          if (def.href != null) attrs.$set(type.attr, def.href);\n        }\n\n        inputAttrs.forEach((field) => {\n          rawDef[field] = attrs[field] ? scope.$eval(attrs[field]) : null;\n\n          attrs.$observe(field, (expr) => {\n            watchDeregFns[field]();\n            watchDeregFns[field] = scope.$watch(\n              expr,\n              (newval) => {\n                rawDef[field] = newval;\n                update();\n              },\n              true\n            );\n          });\n        });\n\n        update();\n\n        scope.$on('$destroy', <any>$uiRouter.stateRegistry.onStatesChanged(update));\n        scope.$on('$destroy', <any>$uiRouter.transitionService.onSuccess({}, update));\n\n        if (!type.clickable) return;\n        hookFn = clickHook(element, $state, $timeout, type, getDef);\n        bindEvents(element, scope, hookFn, rawDef.uiStateOpts);\n      },\n    };\n  },\n];\n\n/**\n * `ui-sref-active` and `ui-sref-active-eq`: A directive that adds a CSS class when a `ui-sref` is active\n *\n * A directive working alongside [[uiSref]] and [[uiState]] to add classes to an element when the\n * related directive's state is active (and remove them when it is inactive).\n *\n * The primary use-case is to highlight the active link in navigation menus,\n * distinguishing it from the inactive menu items.\n *\n * ### Linking to a `ui-sref` or `ui-state`\n * `ui-sref-active` can live on the same element as `ui-sref`/`ui-state`, or it can be on a parent element.\n * If a `ui-sref-active` is a parent to more than one `ui-sref`/`ui-state`, it will apply the CSS class when **any of the links are active**.\n *\n * ### Matching\n *\n * The `ui-sref-active` directive applies the CSS class when the `ui-sref`/`ui-state`'s target state **or any child state is active**.\n * This is a \"fuzzy match\" which uses [[StateService.includes]].\n *\n * The `ui-sref-active-eq` directive applies the CSS class when the `ui-sref`/`ui-state`'s target state is directly active (not when child states are active).\n * This is an \"exact match\" which uses [[StateService.is]].\n *\n * ### Parameter values\n * If the `ui-sref`/`ui-state` includes parameter values, the current parameter values must match the link's values for the link to be highlighted.\n * This allows a list of links to the same state with different parameters to be rendered, and the correct one highlighted.\n *\n * #### Example:\n * ```html\n * <li ng-repeat=\"user in users\" ui-sref-active=\"active\">\n *   <a ui-sref=\"user.details({ userId: user.id })\">{{ user.lastName }}</a>\n * </li>\n * ```\n *\n * ### Examples\n *\n * Given the following template:\n * #### Example:\n * ```html\n * <ul>\n *   <li ui-sref-active=\"active\" class=\"item\">\n *     <a href ui-sref=\"app.user({user: 'bilbobaggins'})\">@bilbobaggins</a>\n *   </li>\n * </ul>\n * ```\n *\n * When the app state is `app.user` (or any child state),\n * and contains the state parameter \"user\" with value \"bilbobaggins\",\n * the resulting HTML will appear as (note the 'active' class):\n *\n * ```html\n * <ul>\n *   <li ui-sref-active=\"active\" class=\"item active\">\n *     <a ui-sref=\"app.user({user: 'bilbobaggins'})\" href=\"/users/bilbobaggins\">@bilbobaggins</a>\n *   </li>\n * </ul>\n * ```\n *\n * ### Glob mode\n *\n * It is possible to pass `ui-sref-active` an expression that evaluates to an object.\n * The objects keys represent active class names and values represent the respective state names/globs.\n * `ui-sref-active` will match if the current active state **includes** any of\n * the specified state names/globs, even the abstract ones.\n *\n * #### Example:\n * Given the following template, with \"admin\" being an abstract state:\n * ```html\n * <div ui-sref-active=\"{'active': 'admin.**'}\">\n *   <a ui-sref-active=\"active\" ui-sref=\"admin.roles\">Roles</a>\n * </div>\n * ```\n *\n * Arrays are also supported as values in the `ngClass`-like interface.\n * This allows multiple states to add `active` class.\n *\n * #### Example:\n * Given the following template, with \"admin.roles\" being the current state, the class will be added too:\n * ```html\n * <div ui-sref-active=\"{'active': ['owner.**', 'admin.**']}\">\n *   <a ui-sref-active=\"active\" ui-sref=\"admin.roles\">Roles</a>\n * </div>\n * ```\n *\n * When the current state is \"admin.roles\" the \"active\" class will be applied to both the `<div>` and `<a>` elements.\n * It is important to note that the state names/globs passed to `ui-sref-active` override any state provided by a linked `ui-sref`.\n *\n * ### Notes:\n *\n * - The class name is interpolated **once** during the directives link time (any further changes to the\n * interpolated value are ignored).\n *\n * - Multiple classes may be specified in a space-separated format: `ui-sref-active='class1 class2 class3'`\n */\nlet uiSrefActiveDirective: ng1_directive;\nuiSrefActiveDirective = [\n  '$state',\n  '$stateParams',\n  '$interpolate',\n  '$uiRouter',\n  function $StateRefActiveDirective(\n    $state: StateService,\n    $stateParams: Obj,\n    $interpolate: IInterpolateService,\n    $uiRouter: UIRouter\n  ) {\n    return {\n      restrict: 'A',\n      controller: [\n        '$scope',\n        '$element',\n        '$attrs',\n        function ($scope: IScope, $element: IAugmentedJQuery, $attrs: any) {\n          let states: StateData[] = [];\n          let activeEqClass: string;\n          let uiSrefActive: any;\n\n          // There probably isn't much point in $observing this\n          // uiSrefActive and uiSrefActiveEq share the same directive object with some\n          // slight difference in logic routing\n          activeEqClass = $interpolate($attrs.uiSrefActiveEq || '', false)($scope);\n\n          try {\n            uiSrefActive = $scope.$eval($attrs.uiSrefActive);\n          } catch (e) {\n            // Do nothing. uiSrefActive is not a valid expression.\n            // Fall back to using $interpolate below\n          }\n          uiSrefActive = uiSrefActive || $interpolate($attrs.uiSrefActive || '', false)($scope);\n          setStatesFromDefinitionObject(uiSrefActive);\n\n          // Allow uiSref to communicate with uiSrefActive[Equals]\n          this.$$addStateInfo = function (newState: string, newParams: Obj) {\n            // we already got an explicit state provided by ui-sref-active, so we\n            // shadow the one that comes from ui-sref\n            if (isObject(uiSrefActive) && states.length > 0) {\n              return;\n            }\n            const deregister = addState(newState, newParams, uiSrefActive);\n            update();\n            return deregister;\n          };\n\n          function updateAfterTransition(trans) {\n            trans.promise.then(update, noop);\n          }\n          $scope.$on('$destroy', setupEventListeners());\n          if ($uiRouter.globals.transition) {\n            updateAfterTransition($uiRouter.globals.transition);\n          }\n\n          function setupEventListeners() {\n            const deregisterStatesChangedListener = $uiRouter.stateRegistry.onStatesChanged(handleStatesChanged);\n            const deregisterOnStartListener = $uiRouter.transitionService.onStart({}, updateAfterTransition);\n            const deregisterStateChangeSuccessListener = $scope.$on('$stateChangeSuccess', update);\n            return function cleanUp() {\n              deregisterStatesChangedListener();\n              deregisterOnStartListener();\n              deregisterStateChangeSuccessListener();\n            };\n          }\n\n          function handleStatesChanged() {\n            setStatesFromDefinitionObject(uiSrefActive);\n          }\n\n          function setStatesFromDefinitionObject(statesDefinition: Obj) {\n            if (isObject(statesDefinition)) {\n              states = [];\n              forEach(statesDefinition, function (stateOrName: StateOrName | Array<StateOrName>, activeClass: string) {\n                // Helper function to abstract adding state.\n                const addStateForClass = function (stateOrName: string, activeClass: string) {\n                  const ref = parseStateRef(stateOrName);\n                  addState(ref.state, $scope.$eval(ref.paramExpr), activeClass);\n                };\n\n                if (isString(stateOrName)) {\n                  // If state is string, just add it.\n                  addStateForClass(stateOrName as string, activeClass);\n                } else if (isArray(stateOrName)) {\n                  // If state is an array, iterate over it and add each array item individually.\n                  forEach(stateOrName, function (stateOrName: string) {\n                    addStateForClass(stateOrName, activeClass);\n                  });\n                }\n              });\n            }\n          }\n\n          function addState(stateName: string, stateParams: Obj, activeClass: string) {\n            const state = $state.get(stateName, stateContext($element));\n\n            const stateInfo = {\n              state: state || { name: stateName },\n              params: stateParams,\n              activeClass: activeClass,\n            };\n\n            states.push(stateInfo);\n\n            return function removeState() {\n              removeFrom(states)(stateInfo);\n            };\n          }\n\n          // Update route state\n          function update() {\n            const splitClasses = (str) => str.split(/\\s/).filter(identity);\n            const getClasses = (stateList: StateData[]) =>\n              stateList\n                .map((x) => x.activeClass)\n                .map(splitClasses)\n                .reduce(unnestR, []);\n\n            const allClasses = getClasses(states).concat(splitClasses(activeEqClass)).reduce(uniqR, []);\n            const fuzzyClasses = getClasses(states.filter((x) => $state.includes(x.state.name, x.params)));\n            const exactlyMatchesAny = !!states.filter((x) => $state.is(x.state.name, x.params)).length;\n            const exactClasses = exactlyMatchesAny ? splitClasses(activeEqClass) : [];\n\n            const addClasses = fuzzyClasses.concat(exactClasses).reduce(uniqR, []);\n            const removeClasses = allClasses.filter((cls) => !inArray(addClasses, cls));\n\n            $scope.$evalAsync(() => {\n              addClasses.forEach((className) => $element.addClass(className));\n              removeClasses.forEach((className) => $element.removeClass(className));\n            });\n          }\n\n          update();\n        },\n      ],\n    };\n  },\n];\n\n/** @hidden */\ninterface Def {\n  uiState: string;\n  href: string;\n  uiStateParams: Obj;\n  uiStateOpts: any;\n}\n/** @hidden */\ninterface StateData {\n  state: StateDeclaration;\n  params: RawParams;\n  activeClass: string;\n}\n\nangular\n  .module('ui.router.state')\n  .directive('uiSref', uiSrefDirective)\n  .directive('uiSrefActive', uiSrefActiveDirective)\n  .directive('uiSrefActiveEq', uiSrefActiveDirective)\n  .directive('uiState', uiStateDirective);\n", "/** @publicapi @module directives */ /** */\nimport {\n  $QLike,\n  ActiveUIView,\n  extend,\n  filter,\n  HookRegOptions,\n  isDefined,\n  isFunction,\n  isString,\n  kebobString,\n  noop,\n  Obj,\n  Param,\n  parse,\n  PathNode,\n  ResolveContext,\n  StateDeclaration,\n  tail,\n  trace,\n  Transition,\n  TransitionService,\n  TypedMap,\n  unnestR,\n  ViewService,\n} from '@uirouter/core';\nimport { IAugmentedJQuery, IInterpolateService, IScope, ITranscludeFunction } from 'angular';\nimport { ng as angular } from '../angular';\nimport { Ng1Controller, Ng1StateDeclaration } from '../interface';\nimport { getLocals } from '../services';\nimport { Ng1ViewConfig } from '../statebuilders/views';\nimport { ng1_directive } from './stateDirectives';\n\n/** @hidden */\nexport type UIViewData = {\n  $cfg: Ng1ViewConfig;\n  $uiView: ActiveUIView;\n};\n\n/** @hidden */\nexport type UIViewAnimData = {\n  $animEnter: Promise<any>;\n  $animLeave: Promise<any>;\n  $$animLeave: { resolve: () => any }; // \"deferred\"\n};\n\n/**\n * `ui-view`: A viewport directive which is filled in by a view from the active state.\n *\n * ### Attributes\n *\n * - `name`: (Optional) A view name.\n *   The name should be unique amongst the other views in the same state.\n *   You can have views of the same name that live in different states.\n *   The ui-view can be targeted in a View using the name ([[Ng1StateDeclaration.views]]).\n *\n * - `autoscroll`: an expression. When it evaluates to true, the `ui-view` will be scrolled into view when it is activated.\n *   Uses [[$uiViewScroll]] to do the scrolling.\n *\n * - `onload`: Expression to evaluate whenever the view updates.\n *\n * #### Example:\n * A view can be unnamed or named.\n * ```html\n * <!-- Unnamed -->\n * <div ui-view></div>\n *\n * <!-- Named -->\n * <div ui-view=\"viewName\"></div>\n *\n * <!-- Named (different style) -->\n * <ui-view name=\"viewName\"></ui-view>\n * ```\n *\n * You can only have one unnamed view within any template (or root html). If you are only using a\n * single view and it is unnamed then you can populate it like so:\n *\n * ```html\n * <div ui-view></div>\n * $stateProvider.state(\"home\", {\n *   template: \"<h1>HELLO!</h1>\"\n * })\n * ```\n *\n * The above is a convenient shortcut equivalent to specifying your view explicitly with the\n * [[Ng1StateDeclaration.views]] config property, by name, in this case an empty name:\n *\n * ```js\n * $stateProvider.state(\"home\", {\n *   views: {\n *     \"\": {\n *       template: \"<h1>HELLO!</h1>\"\n *     }\n *   }\n * })\n * ```\n *\n * But typically you'll only use the views property if you name your view or have more than one view\n * in the same template. There's not really a compelling reason to name a view if its the only one,\n * but you could if you wanted, like so:\n *\n * ```html\n * <div ui-view=\"main\"></div>\n * ```\n *\n * ```js\n * $stateProvider.state(\"home\", {\n *   views: {\n *     \"main\": {\n *       template: \"<h1>HELLO!</h1>\"\n *     }\n *   }\n * })\n * ```\n *\n * Really though, you'll use views to set up multiple views:\n *\n * ```html\n * <div ui-view></div>\n * <div ui-view=\"chart\"></div>\n * <div ui-view=\"data\"></div>\n * ```\n *\n * ```js\n * $stateProvider.state(\"home\", {\n *   views: {\n *     \"\": {\n *       template: \"<h1>HELLO!</h1>\"\n *     },\n *     \"chart\": {\n *       template: \"<chart_thing/>\"\n *     },\n *     \"data\": {\n *       template: \"<data_thing/>\"\n *     }\n *   }\n * })\n * ```\n *\n * #### Examples for `autoscroll`:\n * ```html\n * <!-- If autoscroll present with no expression,\n *      then scroll ui-view into view -->\n * <ui-view autoscroll/>\n *\n * <!-- If autoscroll present with valid expression,\n *      then scroll ui-view into view if expression evaluates to true -->\n * <ui-view autoscroll='true'/>\n * <ui-view autoscroll='false'/>\n * <ui-view autoscroll='scopeVariable'/>\n * ```\n *\n * Resolve data:\n *\n * The resolved data from the state's `resolve` block is placed on the scope as `$resolve` (this\n * can be customized using [[Ng1ViewDeclaration.resolveAs]]).  This can be then accessed from the template.\n *\n * Note that when `controllerAs` is being used, `$resolve` is set on the controller instance *after* the\n * controller is instantiated.  The `$onInit()` hook can be used to perform initialization code which\n * depends on `$resolve` data.\n *\n * #### Example:\n * ```js\n * $stateProvider.state('home', {\n *   template: '<my-component user=\"$resolve.user\"></my-component>',\n *   resolve: {\n *     user: function(UserService) { return UserService.fetchUser(); }\n *   }\n * });\n * ```\n */\nexport let uiView: ng1_directive;\n// eslint-disable-next-line prefer-const\nuiView = [\n  '$view',\n  '$animate',\n  '$uiViewScroll',\n  '$interpolate',\n  '$q',\n  function $ViewDirective(\n    $view: ViewService,\n    $animate: any,\n    $uiViewScroll: any,\n    $interpolate: IInterpolateService,\n    $q: $QLike\n  ) {\n    function getRenderer() {\n      return {\n        enter: function (element: JQuery, target: any, cb: Function) {\n          if (angular.version.minor > 2) {\n            $animate.enter(element, null, target).then(cb);\n          } else {\n            $animate.enter(element, null, target, cb);\n          }\n        },\n        leave: function (element: JQuery, cb: Function) {\n          if (angular.version.minor > 2) {\n            $animate.leave(element).then(cb);\n          } else {\n            $animate.leave(element, cb);\n          }\n        },\n      };\n    }\n\n    function configsEqual(config1: Ng1ViewConfig, config2: Ng1ViewConfig) {\n      return config1 === config2;\n    }\n\n    const rootData = {\n      $cfg: { viewDecl: { $context: $view._pluginapi._rootViewContext() } },\n      $uiView: {},\n    };\n\n    const directive = {\n      count: 0,\n      restrict: 'ECA',\n      terminal: true,\n      priority: 400,\n      transclude: 'element',\n      compile: function (tElement: JQuery, tAttrs: Obj, $transclude: ITranscludeFunction) {\n        return function (scope: IScope, $element: IAugmentedJQuery, attrs: Obj) {\n          const onloadExp = attrs['onload'] || '',\n            autoScrollExp = attrs['autoscroll'],\n            renderer = getRenderer(),\n            inherited = $element.inheritedData('$uiView') || rootData,\n            name = $interpolate(attrs['uiView'] || attrs['name'] || '')(scope) || '$default';\n\n          let previousEl: JQuery, currentEl: JQuery, currentScope: IScope, viewConfig: Ng1ViewConfig;\n\n          const activeUIView: ActiveUIView = {\n            $type: 'ng1',\n            id: directive.count++, // Global sequential ID for ui-view tags added to DOM\n            name: name, // ui-view name (<div ui-view=\"name\"></div>\n            fqn: inherited.$uiView.fqn ? inherited.$uiView.fqn + '.' + name : name, // fully qualified name, describes location in DOM\n            config: null, // The ViewConfig loaded (from a state.views definition)\n            configUpdated: configUpdatedCallback, // Called when the matching ViewConfig changes\n            get creationContext() {\n              // The context in which this ui-view \"tag\" was created\n              const fromParentTagConfig = parse('$cfg.viewDecl.$context')(inherited);\n              // Allow <ui-view name=\"foo\"><ui-view name=\"bar\"></ui-view></ui-view>\n              // See https://github.com/angular-ui/ui-router/issues/3355\n              const fromParentTag = parse('$uiView.creationContext')(inherited);\n              return fromParentTagConfig || fromParentTag;\n            },\n          };\n\n          trace.traceUIViewEvent('Linking', activeUIView);\n\n          function configUpdatedCallback(config?: Ng1ViewConfig) {\n            if (config && !(config instanceof Ng1ViewConfig)) return;\n            if (configsEqual(viewConfig, config)) return;\n            trace.traceUIViewConfigUpdated(activeUIView, config && config.viewDecl && config.viewDecl.$context);\n\n            viewConfig = config;\n            updateView(config);\n          }\n\n          $element.data('$uiView', { $uiView: activeUIView });\n\n          updateView();\n\n          const unregister = $view.registerUIView(activeUIView);\n          scope.$on('$destroy', function () {\n            trace.traceUIViewEvent('Destroying/Unregistering', activeUIView);\n            unregister();\n          });\n\n          function cleanupLastView() {\n            if (previousEl) {\n              trace.traceUIViewEvent('Removing (previous) el', previousEl.data('$uiView'));\n              previousEl.remove();\n              previousEl = null;\n            }\n\n            if (currentScope) {\n              trace.traceUIViewEvent('Destroying scope', activeUIView);\n              currentScope.$destroy();\n              currentScope = null;\n            }\n\n            if (currentEl) {\n              const _viewData = currentEl.data('$uiViewAnim');\n              trace.traceUIViewEvent('Animate out', _viewData);\n              renderer.leave(currentEl, function () {\n                _viewData.$$animLeave.resolve();\n                previousEl = null;\n              });\n\n              previousEl = currentEl;\n              currentEl = null;\n            }\n          }\n\n          function updateView(config?: Ng1ViewConfig) {\n            const newScope = scope.$new();\n            const animEnter = $q.defer(),\n              animLeave = $q.defer();\n\n            const $uiViewData: UIViewData = {\n              $cfg: config,\n              $uiView: activeUIView,\n            };\n\n            const $uiViewAnim: UIViewAnimData = {\n              $animEnter: animEnter.promise,\n              $animLeave: animLeave.promise,\n              $$animLeave: animLeave,\n            };\n\n            /**\n             * @ngdoc event\n             * @name ui.router.state.directive:ui-view#$viewContentLoading\n             * @eventOf ui.router.state.directive:ui-view\n             * @eventType emits on ui-view directive scope\n             * @description\n             *\n             * Fired once the view **begins loading**, *before* the DOM is rendered.\n             *\n             * @param {Object} event Event object.\n             * @param {string} viewName Name of the view.\n             */\n            newScope.$emit('$viewContentLoading', name);\n\n            const cloned = $transclude(newScope, function (clone) {\n              clone.data('$uiViewAnim', $uiViewAnim);\n              clone.data('$uiView', $uiViewData);\n              renderer.enter(clone, $element, function onUIViewEnter() {\n                animEnter.resolve();\n                if (currentScope) currentScope.$emit('$viewContentAnimationEnded');\n\n                if ((isDefined(autoScrollExp) && !autoScrollExp) || scope.$eval(autoScrollExp)) {\n                  $uiViewScroll(clone);\n                }\n              });\n\n              cleanupLastView();\n            });\n\n            currentEl = cloned;\n            currentScope = newScope;\n            /**\n             * @ngdoc event\n             * @name ui.router.state.directive:ui-view#$viewContentLoaded\n             * @eventOf ui.router.state.directive:ui-view\n             * @eventType emits on ui-view directive scope\n             * @description           *\n             * Fired once the view is **loaded**, *after* the DOM is rendered.\n             *\n             * @param {Object} event Event object.\n             */\n            currentScope.$emit('$viewContentLoaded', config || viewConfig);\n            currentScope.$eval(onloadExp);\n          }\n        };\n      },\n    };\n\n    return directive;\n  },\n];\n\n$ViewDirectiveFill.$inject = ['$compile', '$controller', '$transitions', '$view', '$q'];\n\n/** @hidden */\nfunction $ViewDirectiveFill(\n  $compile: angular.ICompileService,\n  $controller: angular.IControllerService,\n  $transitions: TransitionService,\n  $view: ViewService,\n  $q: angular.IQService\n) {\n  const getControllerAs = parse('viewDecl.controllerAs');\n  const getResolveAs = parse('viewDecl.resolveAs');\n\n  return {\n    restrict: 'ECA',\n    priority: -400,\n    compile: function (tElement: JQuery) {\n      const initial = tElement.html();\n      tElement.empty();\n\n      return function (scope: IScope, $element: JQuery) {\n        const data: UIViewData = $element.data('$uiView');\n        if (!data) {\n          $element.html(initial);\n          $compile($element.contents() as any)(scope);\n          return;\n        }\n\n        const cfg: Ng1ViewConfig = data.$cfg || <any>{ viewDecl: {}, getTemplate: noop };\n        const resolveCtx: ResolveContext = cfg.path && new ResolveContext(cfg.path);\n        $element.html(cfg.getTemplate($element, resolveCtx) || initial);\n        trace.traceUIViewFill(data.$uiView, $element.html());\n\n        const link = $compile($element.contents() as any);\n        const controller = cfg.controller as angular.IControllerService;\n        const controllerAs: string = getControllerAs(cfg);\n        const resolveAs: string = getResolveAs(cfg);\n        const locals = resolveCtx && getLocals(resolveCtx);\n\n        scope[resolveAs] = locals;\n\n        if (controller) {\n          const controllerInstance = <Ng1Controller>(\n            $controller(controller, extend({}, locals, { $scope: scope, $element: $element }))\n          );\n          if (controllerAs) {\n            scope[controllerAs] = controllerInstance;\n            scope[controllerAs][resolveAs] = locals;\n          }\n\n          // TODO: Use $view service as a central point for registering component-level hooks\n          // Then, when a component is created, tell the $view service, so it can invoke hooks\n          // $view.componentLoaded(controllerInstance, { $scope: scope, $element: $element });\n          // scope.$on('$destroy', () => $view.componentUnloaded(controllerInstance, { $scope: scope, $element: $element }));\n\n          $element.data('$ngControllerController', controllerInstance);\n          $element.children().data('$ngControllerController', controllerInstance);\n\n          registerControllerCallbacks($q, $transitions, controllerInstance, scope, cfg);\n        }\n\n        // Wait for the component to appear in the DOM\n        if (isString(cfg.component)) {\n          const kebobName = kebobString(cfg.component);\n          const tagRegexp = new RegExp(`^(x-|data-)?${kebobName}$`, 'i');\n\n          const getComponentController = () => {\n            const directiveEl = [].slice\n              .call($element[0].children)\n              .filter((el: Element) => el && el.tagName && tagRegexp.exec(el.tagName));\n\n            return directiveEl && angular.element(directiveEl).data(`$${cfg.component}Controller`);\n          };\n\n          const deregisterWatch = scope.$watch(getComponentController, function (ctrlInstance) {\n            if (!ctrlInstance) return;\n            registerControllerCallbacks($q, $transitions, ctrlInstance, scope, cfg);\n            deregisterWatch();\n          });\n        }\n\n        link(scope);\n      };\n    },\n  };\n}\n\n/** @hidden */\nconst hasComponentImpl = typeof (angular as any).module('ui.router')['component'] === 'function';\n/** @hidden incrementing id */\nlet _uiCanExitId = 0;\n\n/** @hidden TODO: move these callbacks to $view and/or `/hooks/components.ts` or something */\nfunction registerControllerCallbacks(\n  $q: angular.IQService,\n  $transitions: TransitionService,\n  controllerInstance: Ng1Controller,\n  $scope: IScope,\n  cfg: Ng1ViewConfig\n) {\n  // Call $onInit() ASAP\n  if (\n    isFunction(controllerInstance.$onInit) &&\n    !((cfg.viewDecl.component || cfg.viewDecl.componentProvider) && hasComponentImpl)\n  ) {\n    controllerInstance.$onInit();\n  }\n\n  const viewState: Ng1StateDeclaration = tail(cfg.path).state.self;\n\n  const hookOptions: HookRegOptions = { bind: controllerInstance };\n  // Add component-level hook for onUiParamsChanged\n  if (isFunction(controllerInstance.uiOnParamsChanged)) {\n    const resolveContext: ResolveContext = new ResolveContext(cfg.path);\n    const viewCreationTrans = resolveContext.getResolvable('$transition$').data;\n\n    // Fire callback on any successful transition\n    const paramsUpdated = ($transition$: Transition) => {\n      // Exit early if the $transition$ is the same as the view was created within.\n      // Exit early if the $transition$ will exit the state the view is for.\n      if ($transition$ === viewCreationTrans || $transition$.exiting().indexOf(viewState as StateDeclaration) !== -1)\n        return;\n\n      const toParams = $transition$.params('to') as TypedMap<any>;\n      const fromParams = $transition$.params<TypedMap<any>>('from') as TypedMap<any>;\n      const getNodeSchema = (node: PathNode) => node.paramSchema;\n      const toSchema: Param[] = $transition$.treeChanges('to').map(getNodeSchema).reduce(unnestR, []);\n      const fromSchema: Param[] = $transition$.treeChanges('from').map(getNodeSchema).reduce(unnestR, []);\n\n      // Find the to params that have different values than the from params\n      const changedToParams = toSchema.filter((param: Param) => {\n        const idx = fromSchema.indexOf(param);\n        return idx === -1 || !fromSchema[idx].type.equals(toParams[param.id], fromParams[param.id]);\n      });\n\n      // Only trigger callback if a to param has changed or is new\n      if (changedToParams.length) {\n        const changedKeys: string[] = changedToParams.map((x) => x.id);\n        // Filter the params to only changed/new to params.  `$transition$.params()` may be used to get all params.\n        const newValues = filter(toParams, (val, key) => changedKeys.indexOf(key) !== -1);\n        controllerInstance.uiOnParamsChanged(newValues, $transition$);\n      }\n    };\n    $scope.$on('$destroy', <any>$transitions.onSuccess({}, paramsUpdated, hookOptions));\n  }\n\n  // Add component-level hook for uiCanExit\n  if (isFunction(controllerInstance.uiCanExit)) {\n    const id = _uiCanExitId++;\n    const cacheProp = '_uiCanExitIds';\n\n    // Returns true if a redirect transition already answered truthy\n    const prevTruthyAnswer = (trans: Transition) =>\n      !!trans && ((trans[cacheProp] && trans[cacheProp][id] === true) || prevTruthyAnswer(trans.redirectedFrom()));\n\n    // If a user answered yes, but the transition was later redirected, don't also ask for the new redirect transition\n    const wrappedHook = (trans: Transition) => {\n      let promise;\n      const ids = (trans[cacheProp] = trans[cacheProp] || {});\n\n      if (!prevTruthyAnswer(trans)) {\n        promise = $q.when(controllerInstance.uiCanExit(trans));\n        promise.then((val) => (ids[id] = val !== false));\n      }\n      return promise;\n    };\n\n    const criteria = { exiting: viewState.name };\n    $scope.$on('$destroy', <any>$transitions.onBefore(criteria, wrappedHook, hookOptions));\n  }\n}\n\nangular.module('ui.router.state').directive('uiView', <any>uiView);\nangular.module('ui.router.state').directive('uiView', <any>$ViewDirectiveFill);\n", "/** @publicapi @module ng1 */ /** */\n\nimport { ng as angular } from './angular';\nimport { Obj, StateService, StateOrName } from '@uirouter/core';\n\n/**\n * `isState` Filter: truthy if the current state is the parameter\n *\n * Translates to [[StateService.is]] `$state.is(\"stateName\")`.\n *\n * #### Example:\n * ```html\n * <div ng-if=\"'stateName' | isState\">show if state is 'stateName'</div>\n * ```\n */\n$IsStateFilter.$inject = ['$state'];\nfunction $IsStateFilter($state: StateService) {\n  const isFilter: any = function (state: StateOrName, params: Obj, options?: { relative?: StateOrName }) {\n    return $state.is(state, params, options);\n  };\n  isFilter.$stateful = true;\n  return isFilter;\n}\n\n/**\n * `includedByState` Filter: truthy if the current state includes the parameter\n *\n * Translates to [[StateService.includes]]` $state.is(\"fullOrPartialStateName\")`.\n *\n * #### Example:\n * ```html\n * <div ng-if=\"'fullOrPartialStateName' | includedByState\">show if state includes 'fullOrPartialStateName'</div>\n * ```\n */\n$IncludedByStateFilter.$inject = ['$state'];\nfunction $IncludedByStateFilter($state: StateService) {\n  const includesFilter: any = function (state: StateOrName, params: Obj, options: { relative?: StateOrName }) {\n    return $state.includes(state, params, options);\n  };\n  includesFilter.$stateful = true;\n  return includesFilter;\n}\n\nangular.module('ui.router.state').filter('isState', $IsStateFilter).filter('includedByState', $IncludedByStateFilter);\n\nexport { $IsStateFilter, $IncludedByStateFilter };\n", "/** @publicapi @module ng1 */ /** */\nimport { ng as angular } from './angular';\nimport { IServiceProviderFactory } from 'angular';\nimport IAnchorScrollService = angular.IAnchorScrollService;\nimport ITimeoutService = angular.ITimeoutService;\n\nexport interface UIViewScrollProvider {\n  /**\n   * Uses standard anchorScroll behavior\n   *\n   * Reverts [[$uiViewScroll]] back to using the core [`$anchorScroll`](http://docs.angularjs.org/api/ng.$anchorScroll)\n   * service for scrolling based on the url anchor.\n   */\n  useAnchorScroll(): void;\n}\n\n/** @hidden */\nfunction $ViewScrollProvider() {\n  let useAnchorScroll = false;\n\n  this.useAnchorScroll = function () {\n    useAnchorScroll = true;\n  };\n\n  this.$get = [\n    '$anchorScroll',\n    '$timeout',\n    function ($anchorScroll: IAnchorScrollService, $timeout: ITimeoutService): Function {\n      if (useAnchorScroll) {\n        return $anchorScroll;\n      }\n\n      return function ($element: JQuery) {\n        return $timeout(\n          function () {\n            $element[0].scrollIntoView();\n          },\n          0,\n          false\n        );\n      };\n    },\n  ];\n}\n\nangular.module('ui.router.state').provider('$uiViewScroll', <IServiceProviderFactory>$ViewScrollProvider);\n", "/**\n * Main entry point for angular 1.x build\n * @publicapi @module ng1\n */ /** */\nexport * from './interface';\nexport * from './services';\nexport * from './statebuilders/views';\nexport * from './stateProvider';\nexport * from './urlRouterProvider';\n\nimport './injectables';\nimport './directives/stateDirectives';\nimport './stateFilters';\nimport './directives/viewDirective';\nimport './viewScroll';\n\nexport default 'ui.router';\n\nimport * as core from '@uirouter/core';\nexport { core };\nexport * from '@uirouter/core';\n"], "names": ["ng_from_global", "angular", "ng", "ng_from_import", "ng_from_import.module", "getNg1ViewConfigFactory", "templateFactory", "path", "view", "services", "$injector", "get", "Ng1ViewConfig", "hasAny<PERSON>ey", "keys", "obj", "reduce", "acc", "key", "isDefined", "ng1ViewsBuilder", "state", "parent", "compKeys", "nonCompKeys", "concat", "allViewKeys", "views", "Error", "name", "filter", "join", "viewsObject", "$default", "pick", "for<PERSON>ach", "config", "isString", "component", "extend", "resolveAs", "$type", "$context", "$name", "normalized", "ViewService", "normalizeUIViewTarget", "$uiViewName", "uiViewName", "$uiViewContextAnchor", "uiViewContextAnchor", "id", "viewDecl", "factory", "this", "uiView", "context", "_this", "makeComponentTemplate", "bindings", "template", "$q", "ResolveContext", "params", "node", "paramV<PERSON><PERSON>", "promises", "when", "fromConfig", "controller", "getController", "all", "then", "results", "trace", "traceViewServiceEvent", "provider", "controller<PERSON><PERSON><PERSON>", "isInjectable", "deps", "annotate", "providerFn", "isArray", "tail", "Resolvable", "version", "minor", "$http", "$templateCache", "$templateRequest", "has", "TemplateFactory", "value", "_useHttp", "asTemplate", "result", "str", "asComponent", "fromString", "templateUrl", "fromUrl", "templateProvider", "fromProvider", "componentProvider", "fromComponentProvider", "isFunction", "url", "cache", "headers", "Accept", "response", "data", "prefix", "kebob", "camelCase", "kebobed", "kebobString", "exec", "attrs", "cmpDefs", "length", "map", "getBindings", "unnestR", "getComponentBindings", "input", "type", "attrName", "attr", "resolveName", "res", "getResolvable", "fn", "args", "kebobName", "def", "isObject", "bindToController", "scopeBindings", "scope", "bindingsObj", "Object", "tuple", "stateRegistry", "stateService", "createProxyFunctions", "val", "StateProvider", "prototype", "func", "decorator", "definition", "register", "callback", "onInvalid", "getStateHookBuilder", "<PERSON><PERSON><PERSON>", "stateObject", "hook", "pathname", "trans", "subContext", "treeChanges", "$$state", "locals", "getLocals", "$state$", "$transition$", "invoke", "undefined", "$locationProvider", "_lp", "Ng1LocationServices", "router", "pathType", "urlMatcherFactory", "encode", "x", "toString", "replace", "m", "~", "/", "decode", "~~", "~2F", "_urlListeners", "push", "removeFrom", "html5Mode", "enabled", "$sniffer", "history", "_baseHref", "$browser", "baseHref", "$window", "location", "newUrl", "$location", "$rootScope", "$on", "evt", "_loc", "Url<PERSON><PERSON><PERSON><PERSON>rovider", "handler", "match", "$match", "$stateParams", "globals", "urlService", "u<PERSON><PERSON><PERSON><PERSON>", "update", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listen", "ruleFn", "rule", "BaseUrlRule", "locationService", "identity", "rules", "urlRules", "otherwise", "what", "injectable<PERSON>andler", "defer", "deferIntercept", "module", "mod_init", "mod_util", "mod_rtr", "mod_state", "mod_main", "$uiRouterProvider", "U<PERSON><PERSON><PERSON>", "stateProvider", "viewService", "_pluginapi", "_viewConfigFactory", "_decodeParams", "ng1LocationService", "locationConfig", "$get", "_runtimeServices", "monkeyPatchPathParameterType", "$inject", "getProviderFor", "serviceName", "$urp", "service", "runBlock", "$uiRouter", "hasOwnProperty", "call", "checkStrictDi", "error", "strictDi", "resolvables", "resolvable", "resolveFn", "watchDigests", "$watch", "approximateDigests", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url<PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "run", "$urlMatcherFactory", "$state", "$urlRouter", "uiSrefDirective", "uiStateDirective", "uiSrefActiveDirective", "ctx", "getTokens", "getPolicy", "async", "promise", "applyPairs", "parseStateRef", "ref", "paramsOnly", "parsed", "paramExpr", "stateContext", "el", "$uiView", "inheritedData", "parse", "processedDef", "$element", "uiState", "current", "uiStateOpts", "relative", "$current", "inherit", "source", "defaultOpts", "href", "uiStateParams", "getTypeInfo", "isSvg", "prop", "isForm", "nodeName", "isAnchor", "toUpperCase", "clickable", "clickHook", "$timeout", "getDef", "e", "button", "which", "target", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "transition_1", "go", "preventDefault", "ignorePreventDefaultCount_1", "cancel", "bindEvents", "element", "hookFn", "events", "on", "events_1", "_i", "event_1", "off", "events_2", "event_2", "$IsStateFilter", "isFilter", "options", "is", "$stateful", "$IncludedByStateFilter", "includesFilter", "includes", "$ViewDirectiveFill", "$compile", "$controller", "$transitions", "$view", "getControllerAs", "getResolveAs", "restrict", "priority", "compile", "tElement", "initial", "html", "empty", "contents", "cfg", "$cfg", "getTemplate", "noop", "resolveCtx", "traceUIViewFill", "link", "controllerAs", "controllerInstance", "$scope", "children", "registerControllerCallbacks", "tagRegexp_1", "RegExp", "deregisterWatch_1", "directiveEl", "slice", "tagName", "ctrlInstance", "require", "uiSrefActive", "active", "unlinkInfoFn", "rawDef", "uiSref", "$$addStateInfo", "$set", "uiSrefOpts", "$eval", "onStatesChanged", "transitionService", "onSuccess", "inputAttrs", "watchDeregFns", "field", "$observe", "expr", "newval", "$interpolate", "$attrs", "activeEqClass", "deregisterStatesChangedListener", "deregisterOnStartListener", "deregisterStateChangeSuccessListener", "states", "uiSrefActiveEq", "updateAfterTransition", "handleStatesChanged", "setStatesFromDefinitionObject", "statesDefinition", "stateOrName", "activeClass", "addStateForClass", "addState", "stateName", "stateParams", "stateInfo", "splitClasses", "split", "getClasses", "stateList", "allClasses", "uniqR", "fuzzyClasses", "exactClasses", "addClasses", "removeClasses", "cls", "inArray", "$evalAsync", "className", "addClass", "removeClass", "newState", "newParams", "deregister", "onStart", "transition", "directive", "$animate", "$uiViewScroll", "rootData", "_rootViewContext", "count", "terminal", "transclude", "tAttrs", "$transclude", "previousEl", "currentEl", "currentScope", "viewConfig", "onloadExp", "autoScrollExp", "renderer", "enter", "cb", "leave", "inherited", "activeUIView", "fqn", "configUpdated", "config1", "config2", "traceUIViewConfigUpdated", "updateView", "creationContext", "fromParentTagConfig", "fromParentTag", "traceUIViewEvent", "unregister", "registerUIView", "newScope", "$new", "anim<PERSON><PERSON>", "animLeave", "$uiViewData", "$uiViewAnim", "$animEnter", "$animLeave", "$$animLeave", "$emit", "cloned", "clone", "resolve", "remove", "$destroy", "_viewData_1", "cleanupLastView", "hasComponentImpl", "_uiCanExitId", "$onInit", "viewState", "self", "hookOptions", "bind", "uiOnParamsChanged", "viewCreationTrans_1", "exiting", "indexOf", "toParams", "fromParams", "getNodeSchema", "paramSchema", "toSchema", "fromSchema", "changedToParams", "param", "idx", "equals", "changedKeys_1", "newValues", "uiCanExit", "id_1", "prevTruthyAnswer_1", "redirectedFrom", "criteria", "onBefore", "ids", "useAnchorScroll", "$anchorScroll", "scrollIntoView"], "mappings": ";;;;;;;8TAGe,IAAMA,EAAiBC,QACVC,EAAKC,GAAkBC,SAAwBD,EAAiBH,WCqB5EK,IACd,IAAIC,EAAmC,KACvC,OAAO,SAACC,EAAMC,GAEZ,OADAF,EAAkBA,GAAmBG,WAASC,UAAUC,IAAI,oBACrD,CAAC,IAAIC,EAAcL,EAAMC,EAAMF,KAK1C,IAAMO,EAAY,SAACC,EAAMC,GAAQ,OAAAD,EAAKE,QAAO,SAACC,EAAKC,GAAQ,OAAAD,GAAOE,YAAUJ,EAAIG,OAAO,aAavEE,EAAgBC,GAE9B,IAAKA,EAAMC,OAAQ,MAAO,GAE1B,IAEEC,EAAW,CAAC,YAAa,WAAY,qBACrCC,EAHc,CAAC,mBAAoB,cAAe,WAAY,SAAU,SAGlDC,OAFX,CAAC,aAAc,qBAAsB,eAAgB,cAGhEC,EAAcH,EAASE,OAAOD,GAKhC,GAAIL,YAAUE,EAAMM,QAAUd,EAAUa,EAAaL,GACnD,MAAM,IAAIO,MACR,UAAUP,EAAMQ,KAAhB,4JAGMH,EAAYI,QAAO,SAACZ,GAAQ,OAAAC,YAAUE,EAAMH,OAAOa,KAAK,OAIlE,IAAMJ,EAA+C,GACnDK,EAAcX,EAAMM,OAAS,CAAEM,SAAUC,OAAKb,EAAOK,IA6BvD,OA3BAS,UAAQH,GAAa,SAAUI,EAA4BP,GAUzD,GARAA,EAAOA,GAAQ,WAEXQ,WAASD,KAASA,EAAS,CAAEE,UAAmBF,IAGpDA,EAASG,SAAO,GAAIH,GAGhBvB,EAAUU,EAAUa,IAAWvB,EAAUW,EAAaY,GACxD,MAAM,IAAIR,MACR,mBAAmBL,EAASQ,KAAK,eAAcP,EAAYO,KAAK,wBAAuBF,MAAQR,EAAMQ,UAIzGO,EAAOI,UAAYJ,EAAOI,WAAa,WACvCJ,EAAOK,MAAQ,MACfL,EAAOM,SAAWrB,EAClBe,EAAOO,MAAQd,EAEf,IAAMe,EAAaC,cAAYC,sBAAsBV,EAAOM,SAAUN,EAAOO,OAC7EP,EAAOW,YAAcH,EAAWI,WAChCZ,EAAOa,qBAAuBL,EAAWM,oBAEzCvB,EAAME,GAAQO,KAETT,EAIT,IAAIwB,EAAK,eAWP,WAAmB5C,EAAyB6C,EAAqCC,GAAjF,WAAmBC,UAAA/C,EAAyB+C,cAAAF,EAAqCE,aAAAD,EAPjFC,SAAMH,IACNG,aAAS,EA0BTA,iBAAc,SAACC,EAAQC,GACrB,OAAAC,EAAKnB,UACDmB,EAAKJ,QAAQK,sBAAsBH,EAAQC,EAASC,EAAKnB,UAAWmB,EAAKL,SAASO,UAClFF,EAAKG,UAeb,OApCEhD,iBAAA,WAAA,WACQiD,EAAKpD,WAASoD,GACdL,EAAU,IAAIM,iBAAeR,KAAK/C,MAClCwD,EAAST,KAAK/C,KAAKS,QAAO,SAACC,EAAK+C,GAAS,OAAAzB,SAAOtB,EAAK+C,EAAKC,eAAc,IAExEC,EAAgB,CACpBN,SAAUC,EAAGM,KAAKb,KAAKD,QAAQe,WAAWd,KAAKF,SAAUW,EAAQP,IACjEa,WAAYR,EAAGM,KAAKb,KAAKgB,cAAcd,KAGzC,OAAOK,EAAGU,IAAIL,GAAUM,MAAK,SAACC,GAI5B,OAHAC,QAAMC,sBAAsB,SAAUlB,GACtCA,EAAKY,WAAaI,EAAQJ,WAC1B9B,SAAOkB,EAAMgB,EAAQb,UACdH,MAcX7C,0BAAA,SAAc4C,GACZ,IAAMoB,EAAWtB,KAAKF,SAASyB,mBAC/B,IAAKC,eAAaF,GAAW,OAAOtB,KAAKF,SAASiB,WAClD,IAAMU,EAAOtE,WAASC,UAAUsE,SAASJ,GACnCK,EAAaC,UAAQN,GAAYO,OAAUP,GAAYA,EAE7D,OADmB,IAAIQ,aAAW,GAASH,EAAYF,GACrCpE,IAAI6C,sBC/H1B,aAAA,WACyBF,cAAWrD,EAAQoF,QAAQC,MAAQ,EAK3ChC,UAAO,CACpB,QACA,iBACA,YACA,SAACiC,EAAOC,EAAgB9E,GAItB,OAHA+C,EAAKgC,iBAAmB/E,EAAUgF,KAAOhF,EAAUgF,IAAI,qBAAuBhF,EAAUC,IAAI,oBAC5F8C,EAAK8B,MAAQA,EACb9B,EAAK+B,eAAiBA,EACf/B,IAyKb,OApKEkC,2BAAA,SAAeC,GACbtC,KAAKuC,SAAWD,GAgBlBD,uBAAA,SACEvD,EACA2B,EACAP,GAEA,IAEMsC,EAAa,SAACC,GAAW,OAAAtF,WAASoD,GAAGM,KAAK4B,GAAQvB,MAAK,SAACwB,GAAQ,OAAGpC,SAAUoC,OAC7EC,EAAc,SAACF,GAAW,OAAAtF,WAASoD,GAAGM,KAAK4B,GAAQvB,MAAK,SAACwB,GAAQ,OAAG1D,UAAW0D,OAErF,OAAO7E,YAAUiB,EAAOwB,UACpBkC,EAAWxC,KAAK4C,WAAW9D,EAAOwB,SAAUG,IAC5C5C,YAAUiB,EAAO+D,aACjBL,EAAWxC,KAAK8C,QAAQhE,EAAO+D,YAAapC,IAC5C5C,YAAUiB,EAAOiE,kBACjBP,EAAWxC,KAAKgD,aAAalE,EAAOiE,iBAAkBtC,EAAQP,IAC9DrC,YAAUiB,EAAOE,WACjB2D,EAAY7D,EAAOE,WACnBnB,YAAUiB,EAAOmE,mBACjBN,EAAY3C,KAAKkD,sBAAsBpE,EAAOmE,kBAAmBxC,EAAQP,IACzEsC,EAfoB,wBA2B1BH,uBAAA,SAAW/B,EAA6BG,GACtC,OAAO0C,aAAW7C,GAAkBA,EAAUG,GAAUH,GAY1D+B,oBAAA,SAAQe,EAAwB3C,GAE9B,OADI0C,aAAWC,KAAMA,EAAYA,EAAK3C,IAC3B,MAAP2C,EAAoB,KAEpBpD,KAAKuC,SACAvC,KAAKiC,MACT5E,IAAI+F,EAAK,CAAEC,MAAOrD,KAAKkC,eAAgBoB,QAAS,CAAEC,OAAQ,eAC1DrC,MAAK,SAAUsC,GACd,OAAOA,EAASC,QAIfzD,KAAKmC,iBAAiBiB,IAW/Bf,yBAAA,SAAaf,EAAuBb,EAAaP,GAC/C,IAAMuB,EAAOtE,WAASC,UAAUsE,SAASJ,GACnCK,EAAaC,UAAQN,GAAYO,OAAYP,GAAYA,EAE/D,OADmB,IAAIQ,aAAW,GAAcH,EAAYF,GAC1CpE,IAAI6C,IAUxBmC,kCAAA,SAAsBf,EAAuBb,EAAaP,GACxD,IAAMuB,EAAOtE,WAASC,UAAUsE,SAASJ,GACnCK,EAAaC,UAAQN,GAAYO,OAAYP,GAAYA,EAE/D,OADmB,IAAIQ,aAAW,GAAcH,EAAYF,GAC1CpE,IAAI6C,IAiBxBmC,kCAAA,SAAsBpC,EAA0BC,EAAyBlB,EAAmBqB,GAC1FA,EAAWA,GAAY,GAGvB,IAAMqD,EAAS/G,EAAQoF,QAAQC,OAAS,EAAI,KAAO,GAE7C2B,EAAQ,SAACC,GACb,IAAMC,EAAUC,cAAYF,GAC5B,MAAO,aAAaG,KAAKF,GAAW,KAAKA,EAAYA,GAgCjDG,EAOV,SAA8BzF,GAC5B,IAAM0F,EAAiB9G,WAASC,UAAUC,IAAIkB,EAAO,aACrD,IAAK0F,IAAYA,EAAQC,OAAQ,MAAM,IAAI5F,MAAM,mCAAmCC,OACpF,OAAO0F,EAAQE,IAAIC,GAAa1G,OAAO2G,UAAS,IAVhCC,CAAqBtF,GAAWmF,KA7BzB,SAACI,GACZ,IAAAhG,EAAegG,OAATC,EAASD,OACjBE,EAAWd,EAAMpF,GAIvB,GAAI0B,EAAOyE,KAAKD,KAAcpE,EAAS9B,GAAO,OAAUkG,OAAaxE,EAAOyE,KAAKD,OAEjF,IAAME,EAActE,EAAS9B,IAASA,EAGtC,GAAa,MAATiG,EAAc,OAAUC,SAAef,cAAkBiB,QAK7D,GAAa,MAATH,EAAc,CAChB,IAAMI,EAAM1E,EAAQ2E,cAAcF,GAC5BG,EAAKF,GAAOA,EAAInB,KAChBsB,EAAQD,GAAM3H,WAASC,UAAUsE,SAASoD,IAAQ,GAGxD,OAAUL,gBAAsBE,GADZ/C,UAAQkD,GAAM,KAAIA,EAAGZ,OAAS,OAAO,QACIa,EAAKtG,KAAK,UAIzE,OAAUgG,OAAaf,cAAkBiB,SAGqBlG,KAAK,KAC/DuG,EAAYrB,EAAM3E,GACxB,MAAO,IAAIgG,MAAahB,QAAWgB,YAavC,IAAMZ,EAAc,SAACa,GACnB,OAAIC,WAASD,EAAIE,kBAA0BC,EAAcH,EAAIE,kBACtDC,EAAcH,EAAII,QAUrBD,EAAgB,SAACE,GACrB,OAAAC,OAAO/H,KAAK8H,GAAe,IAExBnB,KAAI,SAACvG,GAAQ,MAAA,CAACA,EAAK,oBAAoBmG,KAAKuB,EAAY1H,QAExDY,QAAO,SAACgH,GAAU,OAAA3H,YAAU2H,IAAU5D,UAAQ4D,EAAM,OAEpDrB,KAAI,SAACqB,GAAU,OAAGjH,KAAMiH,EAAM,GAAG,IAAMA,EAAM,GAAIhB,KAAMgB,EAAM,GAAG,qBC/MnE,WAAoBC,EAAsCC,GAAtC1F,mBAAAyF,EAAsCzF,kBAAA0F,EACxDC,uBAAqBC,MAAIC,EAAcC,WAAY9F,KAAM4F,MAAI5F,OAyPjE,OA7JE6F,sBAAA,SAAUtH,EAAcwH,GACtB,OAAO/F,KAAKyF,cAAcO,UAAUzH,EAAMwH,IAAS/F,MAyIrD6F,kBAAA,SAAMtH,EAAW0H,GAOf,OANIf,WAAS3G,GACX0H,EAAa1H,EAEb0H,EAAW1H,KAAOA,EAEpByB,KAAKyF,cAAcS,SAASD,GACrBjG,MAST6F,sBAAA,SAAUM,GACR,OAAOnG,KAAK0F,aAAaU,UAAUD,SC/P1BE,EAAsB,SAACC,GAClC,OAAA,SAA0BC,GACxB,IAAMC,EAAOD,EAAYD,GACnBG,EAAwB,WAAbH,EAAwB,OAAS,KASlD,OAAOE,EAPP,SAA0BE,EAAmB3I,GAC3C,IACM4I,EADiB,IAAInG,iBAAekG,EAAME,YAAYH,IAC1BE,WAAW5I,EAAM8I,WAC7CC,EAAS7H,SAAO8H,EAAUJ,GAAa,CAAEK,QAASjJ,EAAOkJ,aAAcP,IAC7E,OAAOvJ,WAASC,UAAU8J,OAAOV,EAAMxG,KAAM8G,SAGdK,iBCoBnC,WAAYC,GA3BJpH,mBAA4B,GA4BlCA,KAAKoH,kBAAoBA,EACzB,IAAMC,EAAMzB,MAAIwB,GAChBzB,uBAAqB0B,EAAKrH,KAAMqH,EAAK,CAAC,eAwC1C,OAxDSC,+BAAP,SAAoCC,GAClC,IAAMC,EAAsBD,EAAOE,kBAAkBjD,KAAK,QAE1DgD,EAASE,OAAS,SAACC,GACjB,OAAK,MAALA,EAAYA,EAAEC,WAAWC,QAAQ,WAAW,SAACC,GAAM,MAAC,CAAEC,IAAK,KAAMC,IAAK,OAAQF,MAAOH,GAEvFH,EAASS,OAAS,SAACN,GACjB,OAAK,MAALA,EAAYA,EAAEC,WAAWC,QAAQ,aAAa,SAACC,GAAM,MAAC,CAAEI,KAAM,IAAKC,MAAO,KAAML,MAAOH,IAI3FL,oBAAA,aAQAA,qBAAA,SAASnB,GAAT,WAEE,OADAnG,KAAKoI,cAAcC,KAAKlC,GACjB,WAAM,OAAAmC,aAAWnI,EAAKiI,cAAhBE,CAA+BnC,KAG9CmB,sBAAA,WACE,IAAIiB,EAAiBvI,KAAKoH,kBAAkBmB,YAE5C,OADAA,EAAYrD,WAASqD,GAAaA,EAAUC,QAAUD,IAClCvI,KAAKyI,SAASC,SAGpCpB,qBAAA,WACE,OAAOtH,KAAK2I,YAAc3I,KAAK2I,UAAY3I,KAAK4I,SAASC,YAAc7I,KAAK8I,QAAQC,SAAStC,WAG/Fa,gBAAA,SAAI0B,EAAiBnB,EAAiB9J,GAIpC,oBAJmB8J,MACfhK,YAAUmL,IAAShJ,KAAKiJ,UAAU7F,IAAI4F,GACtCnB,GAAS7H,KAAKiJ,UAAUpB,UACxB9J,GAAOiC,KAAKiJ,UAAUlL,MAAMA,GACzBiC,KAAKiJ,UAAU7F,OAGxBkE,6BAAA,SAAiB4B,EAAYD,EAA6BR,EAAUG,EAAUE,GAA9E,WACE9I,KAAKiJ,UAAYA,EACjBjJ,KAAKyI,SAAWA,EAChBzI,KAAK4I,SAAWA,EAChB5I,KAAK8I,QAAUA,EAGfI,EAAWC,IAAI,0BAA0B,SAACC,GAAQ,OAAAjJ,EAAKiI,cAAcvJ,SAAQ,SAACiG,GAAO,OAAAA,EAAGsE,SACxF,IAAMC,EAAOzD,MAAIqD,GAGjBtD,uBAAqB0D,EAAMrJ,KAAMqJ,EAAM,CAAC,UAAW,OAAQ,SAAU,SAErE1D,uBAAqB0D,EAAMrJ,KAAMqJ,EAAM,CAAC,OAAQ,WAAY,4BCzD9D,WAAmC9B,GAAAvH,YAAAuH,EA0KrC,OA/KS+B,oBAAP,SAAyB/B,EAAkBgC,GACzC,OAAO,SAACC,GAAU,OAAArM,WAASC,UAAU8J,OAAOqC,EAAS,KAAM,CAAEE,OAAQD,EAAOE,aAAcnC,EAAOoC,QAAQlJ,WAO3G6I,iBAAA,WACE,IAAMM,EAAa5J,KAAKuH,OAAOqC,WAG/B,OAFA5J,KAAKuH,OAAOsC,UAAUC,QAAO,GACxBF,EAAWG,mBAAmBH,EAAWI,SACvChK,KAAKuH,OAAOsC,WAkCrBP,iBAAA,SAAKW,GAAL,WACE,IAAK9G,aAAW8G,GAAS,MAAM,IAAI3L,MAAM,6BAEzC,IAEM4L,EAAO,IAAIC,eAFH,WAAM,OAAAF,EAAO9M,WAASC,UAAW+C,EAAKoH,OAAO6C,mBAEvBC,YAEpC,OADArK,KAAKuH,OAAOqC,WAAWU,MAAMJ,KAAKA,GAC3BlK,MA6BTsJ,sBAAA,SAAUY,GAAV,WACQK,EAAWvK,KAAKuH,OAAOqC,WAAWU,MACxC,GAAIvL,WAASmL,GACXK,EAASC,UAAUN,OACd,CAAA,IAAI/G,aAAW+G,GAGpB,MAAM,IAAI5L,MAAM,uCAFhBiM,EAASC,WAAU,WAAM,OAAAN,EAAK/M,WAASC,UAAW+C,EAAKoH,OAAO6C,oBAKhE,OAAOpK,MAyCTsJ,iBAAA,SAAKmB,EAAoClB,GAMvC,OALI3H,UAAQ2H,IAAYpG,aAAWoG,MACjCA,EAAUD,EAAkBoB,kBAAkB1K,KAAKuH,OAAQgC,IAG7DvJ,KAAKuH,OAAOqC,WAAWU,MAAMzJ,KAAK4J,EAAMlB,GACjCvJ,MAiCTsJ,2BAAA,SAAeqB,GACb3K,KAAKuH,OAAOqC,WAAWgB,eAAeD,WCnKlCE,OAAO,qBAAsB,IACrC,IAAMC,EAAWnO,EAAQkO,OAAO,iBAAkB,CAAC,OAC7CE,EAAWpO,EAAQkO,OAAO,iBAAkB,CAAC,mBAC7CG,EAAUrO,EAAQkO,OAAO,mBAAoB,CAAC,mBAC9CI,EAAYtO,EAAQkO,OAAO,kBAAmB,CAAC,mBAAoB,iBAAkB,uBACrFK,EAAWvO,EAAQkO,OAAO,YAAa,CAAC,iBAAkB,kBAAmB,uBAY/EtD,GAXa5K,EAAQkO,OAAO,mBAAoB,CAAC,cAW9B,MAIvB,SAASM,EAAkB/D,IAEzBG,EAASvH,KAAKuH,OAAS,IAAI6D,YACpBC,cAAgB,IAAIxF,EAAc0B,EAAO9B,cAAe8B,EAAO7B,cAGtE6B,EAAO9B,cAAcO,UAAU,QAASlI,GACxCyJ,EAAO9B,cAAcO,UAAU,SAAUK,EAAoB,WAC7DkB,EAAO9B,cAAcO,UAAU,WAAYK,EAAoB,aAC/DkB,EAAO9B,cAAcO,UAAU,UAAWK,EAAoB,YAE9DkB,EAAO+D,YAAYC,WAAWC,mBAAmB,MAAOzO,KAGxDwK,EAAOqC,WAAW9K,OAAO2M,eAAgB,EAEzC,IAAMC,EAAsBnE,EAAO6C,gBAAkB7C,EAAOoE,eAAiB,IAAIrE,EAC/EF,GASF,SAASwE,EACP3C,EACAL,EACAE,EACAL,EACAS,EACAjH,EACAC,GAKA,OAHAwJ,EAAmBG,iBAAiB3C,EAAYD,EAAWR,EAAUG,EAAUE,UACxEvB,EAAe,cACfA,EAAa,KACbA,EAET,OApBAD,EAAoBwE,6BAA6BvE,GAGjDA,EAAe,OAAIA,EACnBA,EAAa,KAAIqE,EACjBA,EAAKG,QAAU,CAAC,YAAa,WAAY,UAAW,WAAY,aAAc,QAAS,kBAehFxE,EA1CT4D,EAAkBY,QAAU,CAAC,qBA6C7B,IAAMC,EAAiB,SAACC,GAAgB,MAAA,CACtC,oBACA,SAACC,GACC,IAAMC,EAAUD,EAAK3E,OAAO0E,GAE5B,OADAE,EAAc,KAAI,WAAM,OAAAA,GACjBA,KAMX,SAASC,EAAShP,EAA6BmD,EAAe8L,GAK5D,GAJAlP,WAASC,UAAYA,EACrBD,WAASoD,GAAUA,GAGdgF,OAAOO,UAAUwG,eAAeC,KAAKnP,EAAW,YACnD,IACEA,EAAU8J,QAAO,SAAUsF,OAC3B,MAAOC,GACPrP,EAAUsP,WAAa,cAAc3I,KAAK0I,GAASA,EAAM7E,YAM7DyE,EAAU5G,cACPpI,MACA8G,KAAI,SAACwD,GAAM,OAAAA,EAAEd,UAAU8F,eACvBjP,OAAO2G,UAAS,IAChB7F,QAAO,SAACmJ,GAAM,MAAW,aAAXA,EAAElG,QAChB5C,SAAQ,SAAC+N,GAAe,OAACA,EAAWnL,KAAOrE,EAAUsE,SAASkL,EAAWC,UAAWzP,EAAUsP,aArBnGN,EAASL,QAAU,CAAC,YAAa,KAAM,sBAgCvBe,EAAa5D,GAC3BA,EAAW6D,QAAO,WAChB3L,QAAM4L,wBAHVF,EAAaf,QAAU,CAAC,cAOxBjB,EAASxJ,SAAS,YAAkB6J,GACpCH,EAAQ1J,SAAS,aAAc,CAAC,oBAdH,SAAC2L,GAAuB,OAACA,EAASC,kBAAoB,IAAI5D,EAAkB2D,MAezGlC,EAASzJ,SAAS,cAAe0K,EAAe,eAChDjB,EAASzJ,SAAS,qBAAsB,CAAC,oBAAqB,WAAM,OAAAiG,EAAOE,qBAC3EsD,EAASzJ,SAAS,oBAAoB,WAAM,OAAA,IAAIe,KAChD4I,EAAU3J,SAAS,iBAAkB0K,EAAe,kBACpDf,EAAU3J,SAAS,mBAAoB0K,EAAe,YACtDf,EAAU3J,SAAS,eAAgB0K,EAAe,sBAClDf,EAAU3J,SAAS,SAAU,CAAC,oBAjBL,WAAM,OAAArC,SAAOsI,EAAO8D,cAAe,CAAEO,KAAM,WAAM,OAAArE,EAAO7B,mBAmBjFuF,EAAUlL,QAAQ,eAAgB,CAAC,YAAa,SAACsM,GAAwB,OAAAA,EAAU1C,QAAQlJ,UAC3FyK,EAASnL,QAAQ,SAAS,WAAM,OAAAwH,EAAO+D,eACvCJ,EAASiB,QAAQ,UAAU,WAAM,OAAA/K,WAEjC8J,EAASiC,IAAIL,GACb/B,EAASoC,IAAI,CAAC,qBAAsB,SAAUC,OAC9CnC,EAAUkC,IAAI,CAAC,SAAU,SAAUE,OACnCrC,EAAQmC,IAAI,CAAC,aAAc,SAAUG,OACrCxC,EAASqC,IAAIf,OC8GTmB,EA0IAC,EAsJAC,EChZOxN,EFKE8G,EAAY,SAAC2G,GASxB,OAReA,EAAIC,YAAYnP,OAAOO,YAEhBoF,KAAI,SAACvG,GACzB,IAAMgP,EAAac,EAAI7I,cAAcjH,GAErC,MAAO,CAACA,EAAoB,WADT8P,EAAIE,UAAUhB,GAAYiB,MACNjB,EAAWkB,QAAUlB,EAAWnJ,SAG3D/F,OAAOqQ,aAAY,KChJnC,SAASC,EAAcC,GACrB,IAAMC,EAAaD,EAAIzE,MAAM,qBACzB0E,IAAYD,EAAM,IAAMC,EAAW,GAAK,KAE5C,IAAMC,EAASF,EAAIpG,QAAQ,MAAO,KAAK2B,MAAM,kCAC7C,IAAK2E,GAA4B,IAAlBA,EAAOjK,OAAc,MAAM,IAAI5F,MAAM,sBAAwB2P,EAAM,KAClF,MAAO,CAAElQ,MAAOoQ,EAAO,IAAM,KAAMC,UAAWD,EAAO,IAAM,MAI7D,SAASE,EAAaC,GACpB,IAAMC,EAAuBD,EAAGtQ,SAA8BwQ,cAAc,WACtEvR,EAAmBwR,QAAM,YAANA,CAAmBF,GAC5C,OAAOtR,EAAO4E,OAAK5E,GAAMc,MAAMQ,UAAO4I,EAIxC,SAASuH,EAAarB,EAAsBsB,EAA4B1J,GACtE,IAAM2J,EAAU3J,EAAI2J,SAAWvB,EAAOwB,QAAQtQ,KACxCuQ,EAAc7P,SAyDtB,SAAqBqP,EAAsBjB,GACzC,MAAO,CACL0B,SAAUV,EAAaC,IAAOjB,EAAO2B,SACrCC,SAAS,EACTC,OAAQ,QA7DiBC,CAAYR,EAAUtB,GAASpI,EAAI6J,aAAe,IACvEM,EAAO/B,EAAO+B,KAAKR,EAAS3J,EAAIoK,cAAeP,GACrD,MAAO,CAAEF,UAASS,cAAepK,EAAIoK,cAAeP,cAAaM,QAWnE,SAASE,EAAYhB,GAEnB,IAAMiB,EAA4D,+BAApDhK,OAAOO,UAAU8B,SAAS2E,KAAK+B,EAAGkB,KAAK,SAC/CC,EAA4B,SAAnBnB,EAAG,GAAGoB,SAErB,MAAO,CACLhL,KAAM+K,EAAS,SAAWF,EAAQ,aAAe,OACjDI,SAA+C,MAArCrB,EAAGkB,KAAK,WAAWI,cAC7BC,WAAYJ,GAKhB,SAASK,EACPxB,EACAjB,EACA0C,EACAvL,EACAwL,GAEA,OAAO,SAAUC,GACf,IAAMC,EAASD,EAAEE,OAASF,EAAEC,OAC1BE,EAASJ,IAEX,KAAME,EAAS,GAAKD,EAAEI,SAAWJ,EAAEK,SAAWL,EAAEM,UAAYN,EAAEO,QAAUlC,EAAG5J,KAAK,WAAY,CAE1F,IAAM+L,EAAaV,GAAS,WACrBzB,EAAG5J,KAAK,aACX2I,EAAOqD,GAAGN,EAAOxB,QAASwB,EAAOf,cAAee,EAAOtB,gBAG3DmB,EAAEU,iBAGF,IAAIC,EAA4BpM,EAAKmL,WAAaS,EAAOhB,KAAO,EAAI,EAEpEa,EAAEU,eAAiB,WACbC,KAA+B,GAAGb,EAASc,OAAOJ,MAgB9D,SAASK,EAAWC,EAA2B1L,EAAe2L,EAAuBlC,GACnF,IAAImC,EAEAnC,IACFmC,EAASnC,EAAYmC,QAGlBrP,UAAQqP,KACXA,EAAS,CAAC,UAIZ,IADA,IAAMC,EAAKH,EAAQG,GAAK,KAAO,WACXC,IAAAC,WAAAA,IAAQ,CAAvB,IAAMC,OACTN,EAAQG,GAAIG,EAAOL,GAGrB3L,EAAM8D,IAAI,YAAY,WAEpB,IADA,IAAMmI,EAAMP,EAAQO,IAAM,MAAQ,aACdC,IAAAH,WAAAA,IAAQ,CAAvB,IAAMI,OACTT,EAAQO,GAAKE,EAAOR,OEjI1B,SAASS,EAAepE,GACtB,IAAMqE,EAAgB,SAAU3T,EAAoB0C,EAAakR,GAC/D,OAAOtE,EAAOuE,GAAG7T,EAAO0C,EAAQkR,IAGlC,OADAD,EAASG,WAAY,EACdH,EAcT,SAASI,EAAuBzE,GAC9B,IAAM0E,EAAsB,SAAUhU,EAAoB0C,EAAakR,GACrE,OAAOtE,EAAO2E,SAASjU,EAAO0C,EAAQkR,IAGxC,OADAI,EAAeF,WAAY,EACpBE,EDqUT,SAASE,EACPC,EACAC,EACAC,EACAC,EACA9R,GAEA,IAAM+R,EAAkB7D,QAAM,yBACxB8D,EAAe9D,QAAM,sBAE3B,MAAO,CACL+D,SAAU,MACVC,UAAW,IACXC,QAAS,SAAUC,GACjB,IAAMC,EAAUD,EAASE,OAGzB,OAFAF,EAASG,QAEF,SAAUzN,EAAesJ,GAC9B,IAAMlL,EAAmBkL,EAASlL,KAAK,WACvC,IAAKA,EAGH,OAFAkL,EAASkE,KAAKD,QACdV,EAASvD,EAASoE,WAAlBb,CAAqC7M,GAIvC,IAAM2N,EAAqBvP,EAAKwP,MAAa,CAAEnT,SAAU,GAAIoT,YAAaC,QACpEC,EAA6BJ,EAAI/V,MAAQ,IAAIuD,iBAAewS,EAAI/V,MACtE0R,EAASkE,KAAKG,EAAIE,YAAYvE,EAAUyE,IAAeR,GACvDxR,QAAMiS,gBAAgB5P,EAAK8K,QAASI,EAASkE,QAE7C,IAAMS,EAAOpB,EAASvD,EAASoE,YACzBhS,EAAaiS,EAAIjS,WACjBwS,EAAuBjB,EAAgBU,GACvC9T,EAAoBqT,EAAaS,GACjClM,EAASsM,GAAcrM,EAAUqM,GAIvC,GAFA/N,EAAMnG,GAAa4H,EAEf/F,EAAY,CACd,IAAMyS,EACJrB,EAAYpR,EAAY9B,SAAO,GAAI6H,EAAQ,CAAE2M,OAAQpO,EAAOsJ,SAAUA,KAEpE4E,IACFlO,EAAMkO,GAAgBC,EACtBnO,EAAMkO,GAAcrU,GAAa4H,GAQnC6H,EAASlL,KAAK,0BAA2B+P,GACzC7E,EAAS+E,WAAWjQ,KAAK,0BAA2B+P,GAEpDG,EAA4BpT,EAAI6R,EAAcoB,EAAoBnO,EAAO2N,GAI3E,GAAIjU,WAASiU,EAAIhU,WACf,IAAMgG,EAAYlB,cAAYkP,EAAIhU,WAC5B4U,EAAY,IAAIC,OAAO,eAAe7O,MAAc,KAUpD8O,EAAkBzO,EAAM0H,QARC,WAC7B,IAAMgH,EAAc,GAAGC,MACpBzH,KAAKoC,EAAS,GAAG+E,UACjBlV,QAAO,SAAC8P,GAAgB,OAAAA,GAAMA,EAAG2F,SAAWL,EAAU7P,KAAKuK,EAAG2F,YAEjE,OAAOF,GAAepX,EAAQoU,QAAQgD,GAAatQ,KAAK,IAAIuP,EAAIhU,2BAGL,SAAUkV,GAChEA,IACLP,EAA4BpT,EAAI6R,EAAc8B,EAAc7O,EAAO2N,GACnEc,QAIJR,EAAKjO,MD/JbkI,EAAkB,CAChB,YACA,WACA,SAA4BlB,EAAqB0D,GAC/C,IAAM1C,EAAShB,EAAU3G,aAEzB,MAAO,CACL8M,SAAU,IACV2B,QAAS,CAAC,iBAAkB,oBAC5Bb,KAAM,SAAUjO,EAAe0L,EAA2B/M,EAAYoQ,GACpE,IAAM5P,EAAO8K,EAAYyB,GACnBsD,EAASD,EAAa,IAAMA,EAAa,GAC3CE,EAAyB,KAEvBC,EAAS,GACTvE,EAAS,WAAM,OAAAtB,EAAarB,EAAQ0D,EAASwD,IAE7CtG,EAAMD,EAAchK,EAAMwQ,QAIhC,SAAS1K,IACP,IAAM7E,EAAM+K,IACRsE,GAAcA,IACdD,IAAQC,EAAeD,EAAOI,eAAexP,EAAI2J,QAAS3J,EAAIoK,gBAClD,MAAZpK,EAAImK,MAAcpL,EAAM0Q,KAAKlQ,EAAKE,KAAMO,EAAImK,MAoBlD,GA3BAmF,EAAO3F,QAAUX,EAAIlQ,MACrBwW,EAAOzF,YAAc9K,EAAM2Q,WAAatP,EAAMuP,MAAM5Q,EAAM2Q,YAAc,GASpE1G,EAAIG,YACN/I,EAAM0H,OACJkB,EAAIG,WACJ,SAAUxI,GACR2O,EAAOlF,cAAgBpQ,SAAO,GAAI2G,GAClCkE,OAEF,GAEFyK,EAAOlF,cAAgBpQ,SAAO,GAAIoG,EAAMuP,MAAM3G,EAAIG,aAGpDtE,IAEAzE,EAAM8D,IAAI,WAAiBkD,EAAU5G,cAAcoP,gBAAgB/K,IACnEzE,EAAM8D,IAAI,WAAiBkD,EAAUyI,kBAAkBC,UAAU,GAAIjL,IAEhEtF,EAAKqL,UAAV,CACA,IAAMmB,EAASlB,EAAUiB,EAAS1D,EAAQ0C,EAAUvL,EAAMwL,GAC1Dc,EAAWC,EAAS1L,EAAO2L,EAAQuD,EAAOzF,kBA2FlDtB,EAAmB,CACjB,YACA,WACA,SAAmCnB,EAAqB0D,GACtD,IAAM1C,EAAShB,EAAU3G,aAEzB,MAAO,CACL8M,SAAU,IACV2B,QAAS,CAAC,iBAAkB,oBAC5Bb,KAAM,SAAUjO,EAAe0L,EAA2B/M,EAAYoQ,GACpE,IAGIpD,EAHExM,EAAO8K,EAAYyB,GACnBsD,EAASD,EAAa,IAAMA,EAAa,GAC3CE,EAAyB,KAGvBC,EAAS,GACTvE,EAAS,WAAM,OAAAtB,EAAarB,EAAQ0D,EAASwD,IAE7CS,EAAa,CAAC,UAAW,gBAAiB,eAC1CC,EAAgBD,EAAWtX,QAAO,SAACC,EAAK+G,GAAS,OAAE/G,EAAI+G,GAAQyO,OAAOxV,IAAM,IAElF,SAASmM,IACP,IAAM7E,EAAM+K,IACRsE,GAAcA,IACdD,IAAQC,EAAeD,EAAOI,eAAexP,EAAI2J,QAAS3J,EAAIoK,gBAClD,MAAZpK,EAAImK,MAAcpL,EAAM0Q,KAAKlQ,EAAKE,KAAMO,EAAImK,MAGlD4F,EAAWnW,SAAQ,SAACqW,GAClBX,EAAOW,GAASlR,EAAMkR,GAAS7P,EAAMuP,MAAM5Q,EAAMkR,IAAU,KAE3DlR,EAAMmR,SAASD,GAAO,SAACE,GACrBH,EAAcC,KACdD,EAAcC,GAAS7P,EAAM0H,OAC3BqI,GACA,SAACC,GACCd,EAAOW,GAASG,EAChBvL,OAEF,SAKNA,IAEAzE,EAAM8D,IAAI,WAAiBkD,EAAU5G,cAAcoP,gBAAgB/K,IACnEzE,EAAM8D,IAAI,WAAiBkD,EAAUyI,kBAAkBC,UAAU,GAAIjL,IAEhEtF,EAAKqL,YACVmB,EAASlB,EAAUiB,EAAS1D,EAAQ0C,EAAUvL,EAAMwL,GACpDc,EAAWC,EAAS1L,EAAO2L,EAAQuD,EAAOzF,kBAmGlDrB,EAAwB,CACtB,SACA,eACA,eACA,YACA,SACEJ,EACA3D,EACA4L,EACAjJ,GAEA,MAAO,CACLmG,SAAU,IACVzR,WAAY,CACV,SACA,WACA,SACA,SAAU0S,EAAgB9E,EAA4B4G,GACpD,IACIC,EACApB,EAqCIqB,EACAC,EACAC,EAzCJC,EAAsB,GAO1BJ,EAAgBF,EAAaC,EAAOM,gBAAkB,IAAI,EAA1CP,CAAiD7B,GAEjE,IACEW,EAAeX,EAAOmB,MAAMW,EAAOnB,cACnC,MAAOnE,IAmBT,SAAS6F,EAAsBpP,GAC7BA,EAAMoH,QAAQ5M,KAAK4I,EAAQqJ,QAkB7B,SAAS4C,IACPC,EAA8B5B,GAGhC,SAAS4B,EAA8BC,GACjC/Q,WAAS+Q,KACXL,EAAS,GACT/W,UAAQoX,GAAkB,SAAUC,EAA+CC,GAEjF,IAAMC,EAAmB,SAAUF,EAAqBC,GACtD,IAAMlI,EAAMD,EAAckI,GAC1BG,EAASpI,EAAIlQ,MAAO0V,EAAOmB,MAAM3G,EAAIG,WAAY+H,IAG/CpX,WAASmX,GAEXE,EAAiBF,EAAuBC,GAC/BvU,UAAQsU,IAEjBrX,UAAQqX,GAAa,SAAUA,GAC7BE,EAAiBF,EAAaC,UAOxC,SAASE,EAASC,EAAmBC,EAAkBJ,GACrD,IAEMK,EAAY,CAChBzY,MAHYsP,EAAOhQ,IAAIiZ,EAAWjI,EAAaM,KAG/B,CAAEpQ,KAAM+X,GACxB7V,OAAQ8V,EACRJ,YAAaA,GAKf,OAFAP,EAAOvN,KAAKmO,GAEL,WACLlO,aAAWsN,EAAXtN,CAAmBkO,IAKvB,SAAS1M,IACP,IAAM2M,EAAe,SAAC/T,GAAQ,OAAAA,EAAIgU,MAAM,MAAMlY,OAAO6L,aAC/CsM,EAAa,SAACC,GAClB,OAAAA,EACGzS,KAAI,SAACwD,GAAM,OAAAA,EAAEwO,eACbhS,IAAIsS,GACJ/Y,OAAO2G,UAAS,KAEfwS,EAAaF,EAAWf,GAAQzX,OAAOsY,EAAajB,IAAgB9X,OAAOoZ,QAAO,IAClFC,EAAeJ,EAAWf,EAAOpX,QAAO,SAACmJ,GAAM,OAAA0F,EAAO2E,SAASrK,EAAE5J,MAAMQ,KAAMoJ,EAAElH,YAE/EuW,IADsBpB,EAAOpX,QAAO,SAACmJ,GAAM,OAAA0F,EAAOuE,GAAGjK,EAAE5J,MAAMQ,KAAMoJ,EAAElH,WAASyD,OAC3CuS,EAAajB,GAAiB,GAEjEyB,EAAaF,EAAa5Y,OAAO6Y,GAActZ,OAAOoZ,QAAO,IAC7DI,EAAgBL,EAAWrY,QAAO,SAAC2Y,GAAQ,OAACC,UAAQH,EAAYE,MAEtE1D,EAAO4D,YAAW,WAChBJ,EAAWpY,SAAQ,SAACyY,GAAc,OAAA3I,EAAS4I,SAASD,MACpDJ,EAAcrY,SAAQ,SAACyY,GAAc,OAAA3I,EAAS6I,YAAYF,SA/F9DtB,EADA5B,EAAeA,GAAgBkB,EAAaC,EAAOnB,cAAgB,IAAI,EAAxCkB,CAA+C7B,IAI9EzT,KAAKyU,eAAiB,SAAUgD,EAAkBC,GAGhD,KAAIxS,WAASkP,IAAiBwB,EAAO1R,OAAS,GAA9C,CAGA,IAAMyT,EAAatB,EAASoB,EAAUC,EAAWtD,GAEjD,OADAtK,IACO6N,IAMTlE,EAAOtK,IAAI,YAMHsM,EAAkCpJ,EAAU5G,cAAcoP,gBAAgBkB,GAC1EL,EAA4BrJ,EAAUyI,kBAAkB8C,QAAQ,GAAI9B,GACpEH,EAAuClC,EAAOtK,IAAI,sBAAuBW,GACxE,WACL2L,IACAC,IACAC,OAXAtJ,EAAU1C,QAAQkO,YACpB/B,EAAsBzJ,EAAU1C,QAAQkO,YAgF1C/N,WAsBPe,OAAO,mBACPiN,UAAU,SAAUvK,GACpBuK,UAAU,eAAgBrK,GAC1BqK,UAAU,iBAAkBrK,GAC5BqK,UAAU,UAAWtK,GE5sBxBiE,EAAe1F,QAAU,CAAC,UAmB1B+F,EAAuB/F,QAAU,CAAC,YAS1BlB,OAAO,mBAAmBrM,OAAO,UAAWiT,GAAgBjT,OAAO,kBAAmBsT,GDkI9F7R,EAAS,CACP,QACA,WACA,gBACA,eACA,KACA,SACEoS,EACA0F,EACAC,EACA1C,EACA/U,GAyBA,IAAM0X,EAAW,CACfhF,KAAM,CAAEnT,SAAU,CAAEV,SAAUiT,EAAM9G,WAAW2M,qBAC/C3J,QAAS,IAGLuJ,EAAY,CAChBK,MAAO,EACP3F,SAAU,MACV4F,UAAU,EACV3F,SAAU,IACV4F,WAAY,UACZ3F,QAAS,SAAUC,EAAkB2F,EAAaC,GAChD,OAAO,SAAUlT,EAAesJ,EAA4B3K,GAC1D,IAMIwU,EAAoBC,EAAmBC,EAAsBC,EAN3DC,EAAY5U,EAAc,QAAK,GACnC6U,EAAgB7U,EAAkB,WAClC8U,EArCC,CACLC,MAAO,SAAUhI,EAAiBX,EAAa4I,GACzCrc,EAAQoF,QAAQC,MAAQ,EAC1B+V,EAASgB,MAAMhI,EAAS,KAAMX,GAAQlP,KAAK8X,GAE3CjB,EAASgB,MAAMhI,EAAS,KAAMX,EAAQ4I,IAG1CC,MAAO,SAAUlI,EAAiBiI,GAC5Brc,EAAQoF,QAAQC,MAAQ,EAC1B+V,EAASkB,MAAMlI,GAAS7P,KAAK8X,GAE7BjB,EAASkB,MAAMlI,EAASiI,KA0BxBE,EAAYvK,EAASH,cAAc,YAAcyJ,EACjD1Z,EAAO+W,EAAatR,EAAc,QAAKA,EAAY,MAAK,GAAjDsR,CAAqDjQ,IAAU,WAIlE8T,EAA6B,CACjCha,MAAO,MACPU,GAAIiY,EAAUK,QACd5Z,KAAMA,EACN6a,IAAKF,EAAU3K,QAAQ6K,IAAMF,EAAU3K,QAAQ6K,IAAM,IAAM7a,EAAOA,EAClEO,OAAQ,KACRua,cAaF,SAA+Bva,GAC7B,GAAIA,KAAYA,aAAkBxB,GAAgB,OAClD,GA9Ccgc,EA8CGX,EA9CqBY,EA8CTza,EA7C5Bwa,IAAYC,EA6CyB,OA9C9C,IAAsBD,EAAwBC,EA+CtCnY,QAAMoY,yBAAyBL,EAAcra,GAAUA,EAAOgB,UAAYhB,EAAOgB,SAASV,UAE1FuZ,EAAa7Z,EACb2a,EAAW3a,IAlBX4a,sBAEE,IAAMC,EAAsBlL,QAAM,yBAANA,CAAgCyK,GAGtDU,EAAgBnL,QAAM,0BAANA,CAAiCyK,GACvD,OAAOS,GAAuBC,IAIlCxY,QAAMyY,iBAAiB,UAAWV,GAWlCxK,EAASlL,KAAK,UAAW,CAAE8K,QAAS4K,IAEpCM,IAEA,IAAMK,EAAazH,EAAM0H,eAAeZ,GAgCxC,SAASM,EAAW3a,GAClB,IAAMkb,EAAW3U,EAAM4U,OACjBC,EAAY3Z,EAAGoK,QACnBwP,EAAY5Z,EAAGoK,QAEXyP,EAA0B,CAC9BnH,KAAMnU,EACNyP,QAAS4K,GAGLkB,EAA8B,CAClCC,WAAYJ,EAAUpM,QACtByM,WAAYJ,EAAUrM,QACtB0M,YAAaL,GAefH,EAASS,MAAM,sBAAuBlc,GAEtC,IAAMmc,EAASnC,EAAYyB,GAAU,SAAUW,GAC7CA,EAAMlX,KAAK,cAAe4W,GAC1BM,EAAMlX,KAAK,UAAW2W,GACtBtB,EAASC,MAAM4B,EAAOhM,GAAU,WAC9BuL,EAAUU,UACNlC,GAAcA,EAAa+B,MAAM,+BAEhC5c,YAAUgb,KAAmBA,GAAkBxT,EAAMuP,MAAMiE,KAC9Db,EAAc2C,MAhEtB,WAaE,GAZInC,IACFpX,QAAMyY,iBAAiB,yBAA0BrB,EAAW/U,KAAK,YACjE+U,EAAWqC,SACXrC,EAAa,MAGXE,IACFtX,QAAMyY,iBAAiB,mBAAoBV,GAC3CT,EAAaoC,WACbpC,EAAe,MAGbD,EAAW,CACb,IAAMsC,EAAYtC,EAAUhV,KAAK,eACjCrC,QAAMyY,iBAAiB,cAAekB,GACtCjC,EAASG,MAAMR,GAAW,WACxBsC,EAAUP,YAAYI,UACtBpC,EAAa,QAGfA,EAAaC,EACbA,EAAY,MA8CZuC,MAGFvC,EAAYiC,GACZhC,EAAesB,GAWFS,MAAM,qBAAsB3b,GAAU6Z,GACnDD,EAAa9D,MAAMgE,GAzFrBvT,EAAM8D,IAAI,YAAY,WACpB/H,QAAMyY,iBAAiB,2BAA4BV,GACnDW,UA6FR,OAAOhC,IAIX7F,EAAmBlG,QAAU,CAAC,WAAY,cAAe,eAAgB,QAAS,MAwFlF,IAAMkP,EAAgF,mBAArDte,EAAgBkO,OAAO,aAAwB,UAE5EqQ,EAAe,EAGnB,SAASvH,EACPpT,EACA6R,EACAoB,EACAC,EACAT,IAIE7P,aAAWqQ,EAAmB2H,WAC3BnI,EAAIlT,SAASd,WAAagU,EAAIlT,SAASmD,oBAAsBgY,GAEhEzH,EAAmB2H,UAGrB,IAAMC,EAAiCvZ,OAAKmR,EAAI/V,MAAMc,MAAMsd,KAEtDC,EAA8B,CAAEC,KAAM/H,GAE5C,GAAIrQ,aAAWqQ,EAAmBgI,mBAAoB,CACpD,IACMC,EADiC,IAAIjb,iBAAewS,EAAI/V,MACrB4H,cAAc,gBAAgBpB,KA6BvEgQ,EAAOtK,IAAI,WAAiBiJ,EAAa2C,UAAU,IA1B7B,SAAC9N,GAGrB,GAAIA,IAAiBwU,IAAwF,IAAnExU,EAAayU,UAAUC,QAAQP,GAAzE,CAGA,IAAMQ,EAAW3U,EAAaxG,OAAO,MAC/Bob,EAAa5U,EAAaxG,OAAsB,QAChDqb,EAAgB,SAACpb,GAAmB,OAAAA,EAAKqb,aACzCC,EAAoB/U,EAAaL,YAAY,MAAMzC,IAAI2X,GAAepe,OAAO2G,UAAS,IACtF4X,EAAsBhV,EAAaL,YAAY,QAAQzC,IAAI2X,GAAepe,OAAO2G,UAAS,IAG1F6X,EAAkBF,EAASxd,QAAO,SAAC2d,GACvC,IAAMC,EAAMH,EAAWN,QAAQQ,GAC/B,OAAgB,IAATC,IAAeH,EAAWG,GAAK5X,KAAK6X,OAAOT,EAASO,EAAMtc,IAAKgc,EAAWM,EAAMtc,QAIzF,GAAIqc,EAAgBhY,OAAQ,CAC1B,IAAMoY,EAAwBJ,EAAgB/X,KAAI,SAACwD,GAAM,OAAAA,EAAE9H,MAErD0c,EAAY/d,SAAOod,GAAU,SAAChW,EAAKhI,GAAQ,OAA8B,IAA9B0e,EAAYX,QAAQ/d,MACrE4V,EAAmBgI,kBAAkBe,EAAWtV,OAGkBqU,IAIxE,GAAInY,aAAWqQ,EAAmBgJ,WAAY,CAC5C,IAAMC,EAAKvB,IAILwB,EAAmB,SAAChW,GACxB,QAAEA,IAAWA,EAAe,gBAA8B,IAAzBA,EAAe,cAAE+V,IAAiBC,EAAiBhW,EAAMiW,oBActFC,EAAW,CAAElB,QAASN,EAAU7c,MACtCkV,EAAOtK,IAAI,WAAiBiJ,EAAayK,SAASD,GAZ9B,SAAClW,GACnB,IAAIoH,EACEgP,EAAOpW,EAAe,cAAIA,EAAe,eAAK,GAMpD,OAJKgW,EAAiBhW,KACpBoH,EAAUvN,EAAGM,KAAK2S,EAAmBgJ,UAAU9V,KACvCxF,MAAK,SAAC0E,GAAQ,OAACkX,EAAIL,IAAc,IAAR7W,KAE5BkI,IAIgEwN,OAIrEzQ,OAAO,mBAAmBiN,UAAU,SAAe7X,KACnD4K,OAAO,mBAAmBiN,UAAU,SAAe7F,KE1enDpH,OAAO,mBAAmBvJ,SAAS,iBA5B3C,WACE,IAAIyb,GAAkB,EAEtB/c,KAAK+c,gBAAkB,WACrBA,GAAkB,GAGpB/c,KAAK4L,KAAO,CACV,gBACA,WACA,SAAUoR,EAAqCjN,GAC7C,OAAIgN,EACKC,EAGF,SAAUrO,GACf,OAAOoB,GACL,WACEpB,EAAS,GAAGsO,mBAEd,GACA,mNCtBK"}