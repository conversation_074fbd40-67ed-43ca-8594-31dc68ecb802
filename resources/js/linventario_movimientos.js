document.addEventListener('DOMContentLoaded', function () {
    // Inicializar selectores con búsqueda
    if (typeof Choices !== 'undefined') {
        const selectElements = document.querySelectorAll('select.form-select');
        selectElements.forEach(function(select) {
            new Choices(select, {
                searchEnabled: true,
                itemSelectText: '',
                shouldSort: false
            });
        });
    }
    
    // Handle Filter Form Submission
    const filterForm = document.getElementById('filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', function (event) {
            // Get all filter values
            const fechaInicio = document.getElementById('fecha_inicio').value.trim();
            const fechaFin = document.getElementById('fecha_fin').value.trim();
            const tipoMovimiento = document.getElementById('tipo_movimiento').value;
            const idContenedor = document.getElementById('id_contenedor').value;
            const idActivo = document.getElementById('id_activo').value;
            const idUsuario = document.getElementById('id_usuario').value;
            
            // Check if at least one filter is selected
            if (!fechaInicio && !fechaFin && !tipoMovimiento && !idContenedor && !idActivo && !idUsuario) {
                // Prevent form submission
                event.preventDefault();
                
                // Show SweetAlert message
                showSweetAlertError('Atención', 'Debe aplicar al menos un filtro para buscar movimientos');
                return false;
            }
            
            // Allow form submission if at least one filter is selected
            return true;
        });
    }
    
    // Validación de fechas
    const fechaInicio = document.getElementById('fecha_inicio');
    const fechaFin = document.getElementById('fecha_fin');
    
    if (fechaInicio && fechaFin) {
        fechaInicio.addEventListener('change', function() {
            if (fechaFin.value && fechaInicio.value > fechaFin.value) {
                alert('La fecha de inicio no puede ser posterior a la fecha de fin');
                fechaInicio.value = fechaFin.value;
            }
        });
        
        fechaFin.addEventListener('change', function() {
            if (fechaInicio.value && fechaInicio.value > fechaFin.value) {
                alert('La fecha de fin no puede ser anterior a la fecha de inicio');
                fechaFin.value = fechaInicio.value;
            }
        });
    }
    
    // --- Handle Image Viewing ---
    const tableBody = document.getElementById('movimientos-table-body');
    const imagenModal = document.getElementById('imagen-movimiento-modal');
    const imagenModalLabel = document.getElementById('imagen-movimiento-modal-label');
    const imagenContainer = document.getElementById('imagen-movimiento-container');
    const noImagenMessage = document.getElementById('no-imagen-movimiento-message');
    const loadingImagen = document.getElementById('loading-imagen-movimiento');
    const errorImagen = document.getElementById('error-imagen-movimiento');
    
    // Initialize the modal
    const modal = new bootstrap.Modal(imagenModal);
    
    // Use event delegation for image buttons
    if (tableBody) {
        tableBody.addEventListener('click', function(event) {
            const verImagenButton = event.target.closest('.ver-imagen-btn');
            
            if (verImagenButton) {
                event.preventDefault();
                const movimientoId = verImagenButton.dataset.movimientoId;
                const imagenFilename = verImagenButton.dataset.imagen;
                const activoDescripcion = verImagenButton.dataset.activo || 'Activo';
                
                // Update modal title
                imagenModalLabel.textContent = `Imagen del Movimiento - ${activoDescripcion}`;
                
                // Clear previous content
                imagenContainer.innerHTML = '';
                
                // Show loading indicator and hide other states
                loadingImagen.classList.remove('d-none');
                noImagenMessage.classList.add('d-none');
                errorImagen.classList.add('d-none');
                
                // Show the modal
                modal.show();
                
                // Construct image path
                const imagePath = `resources/uploads/inventario_movimientos/${movimientoId}/${imagenFilename}`;
                
                // Create image element
                const img = document.createElement('img');
                img.className = 'img-fluid';
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
                img.alt = `Imagen del movimiento - ${activoDescripcion}`;
                
                // Handle image load success
                img.onload = function() {
                    loadingImagen.classList.add('d-none');
                    imagenContainer.appendChild(img);
                };
                
                // Handle image load error
                img.onerror = function() {
                    loadingImagen.classList.add('d-none');
                    errorImagen.classList.remove('d-none');
                };
                
                // Start loading the image
                img.src = imagePath;
            }
        });
    }
});
