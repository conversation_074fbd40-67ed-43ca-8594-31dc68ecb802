document.addEventListener('DOMContentLoaded', function () {
    // Form elements
    const form = document.getElementById('prestar-form');
    const contenedorSelect = document.getElementById('id_contenedor');
    const trabajadorSelect = document.getElementById('id_trabajador');
    const activosFeedback = document.getElementById('activos-feedback');
    const activosValidation = document.getElementById('activos-validation');
    const activosCard = document.getElementById('activos-card');
    const selectAllCheckbox = document.getElementById('select-all');
    const activosContainer = document.getElementById('activos-container');
    const activosLoading = document.getElementById('activos-loading');
    const activosEmpty = document.getElementById('activos-empty');
    const activosSelectContainer = document.getElementById('activos-select-container');
    const searchInput = document.getElementById('search-activos');
    const btnPrestar = document.getElementById('btn-prestar');
    const imagenAsociacionSection = document.getElementById('imagen-asociacion-section');
    const activosSeleccionadosContainer = document.getElementById('activos-seleccionados-container');

    // Signature elements
    const signatureCanvas = document.getElementById('signature-pad');
    const clearSignatureBtn = document.getElementById('clear-signature');
    const signatureDataInput = document.getElementById('signature-data');
    const signatureFeedback = document.getElementById('signature-feedback');
    const signatureContainer = document.querySelector('.signature-container');

    // Initialize signature pad
    let signaturePad = null;

    function initializeSignaturePad() {
        if (!signatureCanvas) return;

        // Set canvas size properly to avoid coordinate issues
        const rect = signatureCanvas.getBoundingClientRect();
        const ratio = Math.max(window.devicePixelRatio || 1, 1);

        // Fallback dimensions if getBoundingClientRect returns 0
        const canvasWidth = rect.width > 0 ? rect.width : 600;
        const canvasHeight = rect.height > 0 ? rect.height : 200;

        // Debug logging
        console.log('Initializing signature pad:', {
            rect: { width: rect.width, height: rect.height },
            fallback: { width: canvasWidth, height: canvasHeight },
            ratio: ratio,
            devicePixelRatio: window.devicePixelRatio
        });

        // Set the actual canvas size in memory (scaled for high DPI)
        signatureCanvas.width = canvasWidth * ratio;
        signatureCanvas.height = canvasHeight * ratio;

        // Scale the drawing context so everything draws at the correct size
        const ctx = signatureCanvas.getContext('2d');
        ctx.scale(ratio, ratio);

        // Set the CSS size to maintain the correct display size
        signatureCanvas.style.width = canvasWidth + 'px';
        signatureCanvas.style.height = canvasHeight + 'px';

        // Initialize SignaturePad with proper configuration
        signaturePad = new SignaturePad(signatureCanvas, {
            backgroundColor: 'rgb(255, 255, 255)',
            penColor: 'rgb(0, 0, 0)',
            minWidth: 1,
            maxWidth: 3,
            throttle: 16,
            minDistance: 5,
            velocityFilterWeight: 0.7,
            dotSize: function () {
                return (this.minWidth + this.maxWidth) / 2;
            }
        });

        // Handle signature events
        signaturePad.addEventListener('beginStroke', function() {
            signatureContainer.classList.remove('is-invalid');
            signatureContainer.classList.add('is-valid');
            if (signatureFeedback) {
                signatureFeedback.style.display = 'none';
            }
        });

        signaturePad.addEventListener('endStroke', function() {
            updateSignatureData();
        });

        // Visual feedback that signature pad is ready
        signatureContainer.style.opacity = '1';
        console.log('Signature pad initialized successfully');

        // Test coordinate mapping (optional debug)
        if (window.location.search.includes('debug=signature')) {
            signatureCanvas.addEventListener('click', function(e) {
                const rect = signatureCanvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                console.log('Click coordinates:', { x, y, rect });
            });
        }
    }

    // Initialize the signature pad with a small delay to ensure proper rendering
    if (signatureCanvas) {
        // Use setTimeout to ensure the canvas is fully rendered
        setTimeout(function() {
            initializeSignaturePad();
        }, 100);
    }

    // Signature-related functions
    function updateSignatureData() {
        if (signaturePad && !signaturePad.isEmpty()) {
            const dataURL = signaturePad.toDataURL('image/png');
            signatureDataInput.value = dataURL;
            signatureContainer.classList.remove('is-invalid');
            signatureContainer.classList.add('is-valid');
        } else {
            signatureDataInput.value = '';
            signatureContainer.classList.remove('is-valid');
        }
    }

    function validateSignature() {
        if (!signaturePad || signaturePad.isEmpty()) {
            signatureContainer.classList.add('is-invalid');
            signatureContainer.classList.remove('is-valid');
            if (signatureFeedback) {
                signatureFeedback.style.display = 'block';
            }
            return false;
        } else {
            signatureContainer.classList.remove('is-invalid');
            signatureContainer.classList.add('is-valid');
            if (signatureFeedback) {
                signatureFeedback.style.display = 'none';
            }
            return true;
        }
    }

    function clearSignature() {
        if (signaturePad) {
            signaturePad.clear();
            signatureDataInput.value = '';
            signatureContainer.classList.remove('is-valid', 'is-invalid');
            if (signatureFeedback) {
                signatureFeedback.style.display = 'none';
            }
        }
    }

    // Clear signature button event
    if (clearSignatureBtn) {
        clearSignatureBtn.addEventListener('click', clearSignature);
    }

    // Function to update image association section based on selected assets
    function updateImageAssociationSection() {
        const activosChecked = document.querySelectorAll('.activo-checkbox:checked');

        if (activosChecked.length === 0) {
            // Hide the image association section if no assets are selected
            if (imagenAsociacionSection) {
                imagenAsociacionSection.style.display = 'none';
            }
            return;
        }

        // Show the image association section
        if (imagenAsociacionSection) {
            imagenAsociacionSection.style.display = 'block';
        }

        // Hide all asset upload divs first
        const allAssetUploads = document.querySelectorAll('.asset-image-upload');
        allAssetUploads.forEach(div => {
            div.style.display = 'none';
        });

        // Show upload divs for selected assets and update their labels
        activosChecked.forEach((checkbox) => {
            const activoId = checkbox.value;
            const label = checkbox.nextElementSibling;
            const activoText = label.textContent.trim();

            // Find the corresponding upload div
            const uploadDiv = document.querySelector(`.asset-image-upload[data-asset-id="${activoId}"]`);
            if (uploadDiv) {
                uploadDiv.style.display = 'block';

                // Update the asset name
                const assetNameElement = uploadDiv.querySelector('.asset-name');
                if (assetNameElement) {
                    assetNameElement.textContent = activoText;
                }
            }
        });
    }

    // Function to validate activos selection
    function validateActivosSelection() {
        // If "select all" is checked, we know we have items selected
        if (selectAllCheckbox && selectAllCheckbox.checked) {
            activosFeedback.style.display = 'none';
            activosValidation.setCustomValidity('');
            updateImageAssociationSection();
            return true;
        }

        const activosChecked = document.querySelectorAll('.activo-checkbox:checked');
        if (activosChecked.length === 0) {
            activosFeedback.style.display = 'block';
            activosValidation.setCustomValidity('Please select at least one activo');
            updateImageAssociationSection();
            return false;
        } else {
            activosFeedback.style.display = 'none';
            activosValidation.setCustomValidity('');
            updateImageAssociationSection();
            return true;
        }
    }
    
    // Function to load activos for a contenedor
    function loadActivos(contenedorId) {
        if (!contenedorId) {
            activosContainer.innerHTML = '';
            activosEmpty.classList.add('d-none');
            activosSelectContainer.classList.add('d-none');
            return;
        }
        
        // Show loading indicator
        activosLoading.style.display = 'block';
        activosEmpty.classList.add('d-none');
        activosSelectContainer.classList.add('d-none');
        
        // Fetch activos via AJAX
        fetch(`prestar_activos?ajax=get_activos&id_contenedor=${contenedorId}`)
            .then(response => response.json())
            .then(data => {
                // Hide loading indicator
                activosLoading.style.display = 'none';
                
                if (data.error) {
                    console.error('Error loading activos:', data.error);
                    activosEmpty.textContent = 'Error al cargar activos: ' + data.error;
                    activosEmpty.classList.remove('d-none');
                    return;
                }
                
                if (!data.length) {
                    activosEmpty.classList.remove('d-none');
                    return;
                }
                
                // Show activos container
                activosSelectContainer.classList.remove('d-none');
                
                // Build activos checkboxes
                let html = '';
                data.forEach(activo => {
                    let descripcion = activo.activo_descripcion;
                    let detalles = '';
                    
                    if (activo.marca || activo.modelo) {
                        detalles = `<small class="d-block text-muted">${(activo.marca || '') + ' ' + (activo.modelo || '')}</small>`;
                    }
                    
                    html += `
                    <div class="col-md-4 mb-2 activo-item">
                        <div class="form-check">
                            <input class="form-check-input activo-checkbox" type="checkbox" name="activos[]" value="${activo.id_activo}" id="activo-${activo.id_activo}">
                            <label class="form-check-label" for="activo-${activo.id_activo}">
                                ${descripcion}
                                ${detalles}
                            </label>
                        </div>
                    </div>`;
                });
                
                activosContainer.innerHTML = html;
                
                // Add event listeners to new checkboxes
                document.querySelectorAll('.activo-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', validateActivosSelection);
                });
                
                // Reset select all checkbox
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
                
                // Update validation state
                validateActivosSelection();
            })
            .catch(error => {
                console.error('Error:', error);
                activosLoading.style.display = 'none';
                activosEmpty.textContent = 'Error al cargar activos. Por favor, inténtelo de nuevo.';
                activosEmpty.classList.remove('d-none');
            });
    }
    
    // Event listener for contenedor select change
    if (contenedorSelect) {
        contenedorSelect.addEventListener('change', function() {
            const contenedorId = this.value;
            loadActivos(contenedorId);
        });
        
        // Load activos for initial contenedor if selected
        if (contenedorSelect.value) {
            loadActivos(contenedorSelect.value);
        }
    }
    
    // Add event listeners to checkboxes to update validation state
    document.querySelectorAll('.activo-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', validateActivosSelection);
    });
    
    // Update validation when "select all" is clicked
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.activo-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            
            // Hide error message immediately when "select all" is checked
            if (selectAllCheckbox.checked) {
                activosFeedback.style.display = 'none';
                activosValidation.setCustomValidity('');
            } else {
                // Only validate if unchecking "select all"
                validateActivosSelection();
            }
        });
    }
    
    // Form submission validation
    if (form) {
        form.addEventListener('submit', function(event) {
            // Debug: Log form data before submission (but allow submission)
            console.log('=== FORM SUBMISSION DEBUG ===');
            console.log('Form submission started');

            // Log all form data
            const formData = new FormData(form);
            console.log('Form data entries:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}:`, value);
            }

            // Validate activos selection
            const activosValid = validateActivosSelection();

            // Validate signature
            const signatureValid = validateSignature();

            // Check form validity
            if (!form.checkValidity() || !activosValid || !signatureValid) {
                event.preventDefault();
                event.stopPropagation();
                console.log('Form validation failed - preventing submission');

                // Show specific validation messages
                if (!activosValid) {
                    console.log('- Activos validation failed');
                }
                if (!signatureValid) {
                    console.log('- Signature validation failed');
                }
            } else {
                console.log('Form validation passed - submitting to server...');
                // Update signature data one final time before submission
                updateSignatureData();
                // Allow form to submit normally
            }

            // Add validation classes
            form.classList.add('was-validated');
        });
    }
    
    // Initialize select2 for better dropdown experience (if available)
    if (typeof $.fn.select2 !== 'undefined') {
        $('#id_contenedor, #id_trabajador').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    }
    
    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = searchInput.value.toLowerCase();
            const activoItems = document.querySelectorAll('.activo-item');
            
            activoItems.forEach(item => {
                const label = item.querySelector('.form-check-label').textContent.toLowerCase();
                if (label.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Handle window resize for signature pad
    function resizeSignaturePad() {
        if (signaturePad && signatureCanvas) {
            // Save current signature data if it exists
            const signatureData = signaturePad.isEmpty() ? null : signaturePad.toData();

            // Reinitialize the signature pad with proper sizing
            initializeSignaturePad();

            // Restore signature data if it existed
            if (signatureData && signatureData.length > 0) {
                signaturePad.fromData(signatureData);
            }
        }
    }

    // Add resize event listener
    if (signatureCanvas) {
        window.addEventListener('resize', resizeSignaturePad);
    }
});
