<?php
#region region DOCS

/** @var InventarioMovimiento[] $movimientos */
/** @var Contenedor[] $contenedores */
/** @var Activo[] $activos */
/** @var Usuario[] $usuarios */
/** @var array $tipos_movimiento */
/** @var string|null $fecha_inicio */
/** @var string|null $fecha_fin */
/** @var string|null $tipo_movimiento */
/** @var int|null $id_contenedor */
/** @var int|null $id_activo */
/** @var int|null $id_usuario */
/** @var bool $filter_applied */

use App\classes\InventarioMovimiento;
use App\classes\Contenedor;
use App\classes\Activo;
use App\classes\Usuario;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Movimientos de Inventario</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
    <link href="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Movimientos de Inventario</h4>
				<p class="mb-0 text-muted">Consulta de movimientos de inventario</p>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FILTROS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Filtros de Búsqueda
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="panel-body">
				<form id="filter-form" method="POST" action="linventario_movimientos">
					<div class="row">
						<!-- Filtro de Fecha -->
						<div class="col-md-6">
							<div class="row">
								<div class="col-md-6 mb-3">
									<label for="fecha_inicio" class="form-label">Fecha Inicio:</label>
									<div class="input-group">
										<input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio" 
											value="<?php echo $fecha_inicio ? date('Y-m-d', strtotime($fecha_inicio)) : ''; ?>" autocomplete="off">
										<span class="input-group-text">
											<i class="fa fa-calendar-alt"></i>
										</span>
									</div>
								</div>
								<div class="col-md-6 mb-3">
									<label for="fecha_fin" class="form-label">Fecha Fin:</label>
									<div class="input-group">
										<input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
											value="<?php echo $fecha_fin ? date('Y-m-d', strtotime($fecha_fin)) : ''; ?>" autocomplete="off">
										<span class="input-group-text">
											<i class="fa fa-calendar-alt"></i>
										</span>
									</div>
								</div>
							</div>
						</div>
						
						<!-- Filtro de Tipo de Movimiento -->
						<div class="col-md-6 mb-3">
							<label for="tipo_movimiento" class="form-label">Tipo de Movimiento:</label>
							<select class="form-select" id="tipo_movimiento" name="tipo_movimiento">
								<option value="">Todos</option>
								<?php foreach ($tipos_movimiento as $tipo): ?>
									<option value="<?php echo htmlspecialchars($tipo); ?>" <?php echo $tipo_movimiento === $tipo ? 'selected' : ''; ?>>
										<?php echo htmlspecialchars($tipo); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
						
						<!-- Filtro de Contenedor -->
						<div class="col-md-4 mb-3">
							<label for="id_contenedor" class="form-label">Contenedor:</label>
							<select class="form-select" id="id_contenedor" name="id_contenedor">
								<option value="">Todos</option>
								<?php foreach ($contenedores as $contenedor): ?>
									<option value="<?php echo $contenedor->getId(); ?>" <?php echo $id_contenedor == $contenedor->getId() ? 'selected' : ''; ?>>
										<?php echo htmlspecialchars($contenedor->getDescripcion()); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
						
						<!-- Filtro de Activo -->
						<div class="col-md-4 mb-3">
							<label for="id_activo" class="form-label">Activo:</label>
							<select class="form-select" id="id_activo" name="id_activo">
								<option value="">Todos</option>
								<?php foreach ($activos as $activo): ?>
									<option value="<?php echo $activo->getId(); ?>" <?php echo $id_activo == $activo->getId() ? 'selected' : ''; ?>>
										<?php echo htmlspecialchars($activo->getDescripcion()); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
						
						<!-- Filtro de Usuario -->
						<div class="col-md-4 mb-3">
							<label for="id_usuario" class="form-label">Usuario:</label>
							<select class="form-select" id="id_usuario" name="id_usuario">
								<option value="">Todos</option>
								<?php foreach ($usuarios as $usuario): ?>
									<option value="<?php echo $usuario->getId(); ?>" <?php echo $id_usuario == $usuario->getId() ? 'selected' : ''; ?>>
										<?php echo htmlspecialchars($usuario->getNombre()); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
					</div>
					
					<div class="row">
						<div class="col-12 text-end">
							<button type="submit" class="btn btn-primary">
								<i class="fa fa-search me-1"></i> Buscar
							</button>
							<a href="linventario_movimientos" class="btn btn-default">
								<i class="fa fa-redo me-1"></i> Limpiar Filtros
							</a>
						</div>
					</div>
				</form>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion FILTROS ?>
		
		<?php #region region PANEL MOVIMIENTOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Movimientos de Inventario
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE MOVIMIENTOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Fecha</th>
						<th style="text-align: center;">Tipo</th>
						<th>Contenedor</th>
						<th>Activo</th>
						<th style="text-align: center;">Cantidad</th>
						<th>Usuario</th>
						<th>Trabajador</th>
						<th style="text-align: center;">Imagen</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="movimientos-table-body">
					<?php if (!empty($movimientos)): ?>
						<?php foreach ($movimientos as $movimiento): ?>
							<tr>
								<td><?php echo htmlspecialchars($movimiento->getFecha()); ?></td>
								<td style="text-align: center;">
									<?php if ($movimiento->getTipo_movimiento() === 'entrada'): ?>
										<span class="badge bg-success">Entrada</span>
									<?php else: ?>
										<span class="badge bg-danger">Salida</span>
									<?php endif; ?>
								</td>
								<td><?php echo htmlspecialchars($movimiento->getContenedor_descripcion() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($movimiento->getActivo_descripcion() ?? 'N/A'); ?></td>
								<td style="text-align: center;"><?php echo htmlspecialchars($movimiento->getCantidad()); ?></td>
								<td><?php echo htmlspecialchars($movimiento->getUsuario_nombre() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($movimiento->getTrabajador_nombre() ?? 'N/A'); ?></td>
								<td style="text-align: center;">
									<?php if (!empty($movimiento->getImagen())): ?>
										<button type="button" class="btn btn-xs btn-outline-primary ver-imagen-btn"
												data-movimiento-id="<?php echo $movimiento->getId(); ?>"
												data-imagen="<?php echo htmlspecialchars($movimiento->getImagen()); ?>"
												data-activo="<?php echo htmlspecialchars($movimiento->getActivo_descripcion() ?? 'Activo'); ?>"
												title="Ver imagen del movimiento">
											<i class="fa fa-eye"></i>
										</button>
									<?php else: ?>
										<button type="button" class="btn btn-xs btn-outline-secondary" disabled title="Sin imagen">
											<i class="fa fa-eye-slash"></i>
										</button>
									<?php endif; ?>
								</td>
							</tr>
						<?php endforeach; ?>
					<?php elseif (isset($filter_applied) && $filter_applied): ?>
						<tr>
							<td colspan="8" class="text-center">No se encontraron movimientos con los filtros aplicados.</td>
						</tr>
					<?php else: ?>
						<tr>
							<td colspan="8" class="text-center">
								<div class="alert alert-info mb-0">
									<i class="fa fa-info-circle me-2"></i> Por favor, aplique al menos un filtro para ver los movimientos de inventario.
								</div>
							</td>
						</tr>
					<?php endif; ?>
					</tbody>
				</table>
				<?php #endregion TABLE MOVIMIENTOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL MOVIMIENTOS ?>

		<!-- Modal for displaying movement image -->
		<div class="modal fade" id="imagen-movimiento-modal" tabindex="-1" role="dialog" aria-labelledby="imagen-movimiento-modal-label" aria-hidden="true">
			<div class="modal-dialog modal-lg" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="imagen-movimiento-modal-label">Imagen del Movimiento</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div id="imagen-movimiento-container" class="text-center">
							<!-- Image will be loaded here -->
						</div>
						<div id="no-imagen-movimiento-message" class="text-center p-3 d-none">
							<p>Este movimiento no tiene imagen asociada.</p>
						</div>
						<div id="loading-imagen-movimiento" class="text-center p-3">
							<div class="spinner-border" role="status">
								<span class="visually-hidden">Cargando...</span>
							</div>
							<p class="mt-2">Cargando imagen...</p>
						</div>
						<div id="error-imagen-movimiento" class="text-center p-3 d-none">
							<div class="alert alert-warning">
								<i class="fa fa-exclamation-triangle me-2"></i>
								<span>No se pudo cargar la imagen. El archivo puede no existir.</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<?php #region region JS date ?>
<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA_RESOURCES ?>js/datepickerini.js"></script>
<?php #endregion JS date ?>

<script src="<?php echo RUTA_RESOURCES ?>js/linventario_movimientos.js"></script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
